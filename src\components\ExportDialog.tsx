"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import { useGradientActions } from "@/hooks/useGradients";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import {
  Download,
  Copy,
  Check,
  FileText,
  Code,
  Palette,
  Image,
} from "lucide-react";

interface ExportDialogProps {
  gradient: {
    id: string;
    name: string;
    colors: string[];
    css: string;
    tailwind: string;
  };
  children: React.ReactNode;
}

export default function ExportDialog({
  gradient,
  children,
}: ExportDialogProps) {
  const [copiedFormat, setCopiedFormat] = useState<string | null>(null);
  const { trackCopy, trackDownload } = useGradientActions();

  const copyToClipboard = async (text: string, format: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedFormat(format);
      setTimeout(() => setCopiedFormat(null), 2000);

      // Track copy action
      if (gradient.id) {
        trackCopy(gradient.id, format);
      }
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  };

  const generateCSS = () => {
    return `/* ${gradient.name} */
.gradient-${gradient.id} {
  background: ${gradient.css};
}

/* Alternative syntax */
.gradient-${gradient.id}-alt {
  background-image: ${gradient.css};
}`;
  };

  const generateSCSS = () => {
    return `// ${gradient.name}
$gradient-${gradient.id}: ${gradient.css};

.gradient-${gradient.id} {
  background: $gradient-${gradient.id};
}

@mixin gradient-${gradient.id} {
  background: $gradient-${gradient.id};
}`;
  };

  const generateTailwind = () => {
    return `<!-- ${gradient.name} -->
<div class="${gradient.tailwind}">
  <!-- Your content here -->
</div>

<!-- With custom CSS -->
<div class="bg-gradient-to-r" style="background: ${gradient.css}">
  <!-- Your content here -->
</div>`;
  };

  const generateReact = () => {
    return `// ${gradient.name}
const gradientStyle = {
  background: '${gradient.css}'
};

// Usage in component
<div style={gradientStyle}>
  {/* Your content here */}
</div>

// Or inline
<div style={{ background: '${gradient.css}' }}>
  {/* Your content here */}
</div>`;
  };

  const generateSwiftUI = () => {
    const colors = gradient.colors
      .map((color) => {
        const r = parseInt(color.slice(1, 3), 16) / 255;
        const g = parseInt(color.slice(3, 5), 16) / 255;
        const b = parseInt(color.slice(5, 7), 16) / 255;
        return `Color(red: ${r.toFixed(3)}, green: ${g.toFixed(
          3
        )}, blue: ${b.toFixed(3)})`;
      })
      .join(",\n    ");

    return `// ${gradient.name}
LinearGradient(
  colors: [
    ${colors}
  ],
  startPoint: .topLeading,
  endPoint: .bottomTrailing
)`;
  };

  const generateFlutter = () => {
    const colors = gradient.colors
      .map((color) => `Color(0xFF${color.slice(1)})`)
      .join(",\n      ");

    return `// ${gradient.name}
Container(
  decoration: BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        ${colors}
      ],
    ),
  ),
  child: // Your widget here
)`;
  };

  const generateAndroid = () => {
    return `<!-- ${gradient.name} -->
<!-- res/drawable/gradient_${gradient.id}.xml -->
<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">
    <gradient
        android:startColor="${gradient.colors[0]}"
        android:endColor="${gradient.colors[gradient.colors.length - 1]}"
        android:angle="135" />
</shape>

<!-- Usage in layout -->
<View
    android:layout_width="match_parent"
    android:layout_height="200dp"
    android:background="@drawable/gradient_${gradient.id}" />`;
  };

  const generateJSON = () => {
    return JSON.stringify(
      {
        name: gradient.name,
        id: gradient.id,
        colors: gradient.colors,
        css: gradient.css,
        tailwind: gradient.tailwind,
        formats: {
          hex: gradient.colors,
          rgb: gradient.colors.map((hex) => {
            const r = parseInt(hex.slice(1, 3), 16);
            const g = parseInt(hex.slice(3, 5), 16);
            const b = parseInt(hex.slice(5, 7), 16);
            return `rgb(${r}, ${g}, ${b})`;
          }),
          hsl: gradient.colors.map((hex) => {
            const r = parseInt(hex.slice(1, 3), 16) / 255;
            const g = parseInt(hex.slice(3, 5), 16) / 255;
            const b = parseInt(hex.slice(5, 7), 16) / 255;
            const max = Math.max(r, g, b);
            const min = Math.min(r, g, b);
            const diff = max - min;
            const sum = max + min;
            const l = sum / 2;
            let h = 0;
            let s = 0;

            if (diff !== 0) {
              s = l > 0.5 ? diff / (2 - sum) : diff / sum;
              switch (max) {
                case r:
                  h = (g - b) / diff + (g < b ? 6 : 0);
                  break;
                case g:
                  h = (b - r) / diff + 2;
                  break;
                case b:
                  h = (r - g) / diff + 4;
                  break;
              }
              h /= 6;
            }

            return `hsl(${Math.round(h * 360)}, ${Math.round(
              s * 100
            )}%, ${Math.round(l * 100)}%)`;
          }),
        },
      },
      null,
      2
    );
  };

  const downloadAsFile = (
    content: string,
    filename: string,
    type: string,
    format: string
  ) => {
    const blob = new Blob([content], { type });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    // Track download action
    if (gradient.id) {
      trackDownload(gradient.id, format);
    }
  };

  const exportFormats = [
    {
      id: "css",
      name: "CSS",
      content: generateCSS(),
      filename: `gradient-${gradient.id}.css`,
      type: "text/css",
    },
    {
      id: "scss",
      name: "SCSS",
      content: generateSCSS(),
      filename: `gradient-${gradient.id}.scss`,
      type: "text/scss",
    },
    {
      id: "tailwind",
      name: "Tailwind",
      content: generateTailwind(),
      filename: `gradient-${gradient.id}.html`,
      type: "text/html",
    },
    {
      id: "react",
      name: "React",
      content: generateReact(),
      filename: `gradient-${gradient.id}.jsx`,
      type: "text/javascript",
    },
    {
      id: "swiftui",
      name: "SwiftUI",
      content: generateSwiftUI(),
      filename: `gradient-${gradient.id}.swift`,
      type: "text/swift",
    },
    {
      id: "flutter",
      name: "Flutter",
      content: generateFlutter(),
      filename: `gradient-${gradient.id}.dart`,
      type: "text/dart",
    },
    {
      id: "android",
      name: "Android",
      content: generateAndroid(),
      filename: `gradient_${gradient.id}.xml`,
      type: "text/xml",
    },
    {
      id: "json",
      name: "JSON",
      content: generateJSON(),
      filename: `gradient-${gradient.id}.json`,
      type: "application/json",
    },
  ];

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export "{gradient.name}"
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Preview */}
          <div className="space-y-3">
            <h3 className="font-semibold">Preview</h3>
            <div
              className="w-full h-24 rounded-lg border"
              style={{ background: gradient.css }}
            />
            <div className="flex flex-wrap gap-2">
              {gradient.colors.map((color, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div
                    className="w-6 h-6 rounded border"
                    style={{ backgroundColor: color }}
                  />
                  <code className="text-sm">{color}</code>
                </div>
              ))}
            </div>
          </div>

          {/* Export Formats */}
          <div className="space-y-3">
            <h3 className="font-semibold">Export Formats</h3>
            <Tabs defaultValue="css" className="w-full">
              <TabsList className="grid grid-cols-4 lg:grid-cols-8 w-full">
                {exportFormats.map((format) => (
                  <TabsTrigger
                    key={format.id}
                    value={format.id}
                    className="text-xs"
                  >
                    {format.name}
                  </TabsTrigger>
                ))}
              </TabsList>

              {exportFormats.map((format) => (
                <TabsContent
                  key={format.id}
                  value={format.id}
                  className="space-y-3"
                >
                  <div className="flex items-center justify-between">
                    <Badge variant="outline">{format.name}</Badge>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          copyToClipboard(format.content, format.id)
                        }
                      >
                        {copiedFormat === format.id ? (
                          <>
                            <Check className="h-4 w-4 mr-2" /> Copied!
                          </>
                        ) : (
                          <>
                            <Copy className="h-4 w-4 mr-2" /> Copy
                          </>
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          downloadAsFile(
                            format.content,
                            format.filename,
                            format.type,
                            format.id
                          )
                        }
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Button>
                    </div>
                  </div>
                  <Textarea
                    value={format.content}
                    readOnly
                    className="font-mono text-sm min-h-[200px]"
                  />
                </TabsContent>
              ))}
            </Tabs>
          </div>

          {/* Quick Actions */}
          <div className="flex flex-wrap gap-2 pt-4 border-t">
            <Button
              variant="outline"
              size="sm"
              onClick={() => copyToClipboard(gradient.css, "quick-css")}
            >
              {copiedFormat === "quick-css" ? (
                <>
                  <Check className="h-4 w-4 mr-2" /> Copied!
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4 mr-2" /> Copy CSS
                </>
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                copyToClipboard(gradient.tailwind, "quick-tailwind")
              }
            >
              {copiedFormat === "quick-tailwind" ? (
                <>
                  <Check className="h-4 w-4 mr-2" /> Copied!
                </>
              ) : (
                <>
                  <Palette className="h-4 w-4 mr-2" /> Copy Tailwind
                </>
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                copyToClipboard(gradient.colors.join(", "), "quick-colors")
              }
            >
              {copiedFormat === "quick-colors" ? (
                <>
                  <Check className="h-4 w-4 mr-2" /> Copied!
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4 mr-2" /> Copy Colors
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
