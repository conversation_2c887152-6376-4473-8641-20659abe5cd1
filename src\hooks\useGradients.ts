"use client";

import { useState, useEffect } from 'react';

interface Gradient {
  _id: string;
  name: string;
  description?: string;
  colors: string[];
  css: string;
  tailwind: string;
  type: 'linear' | 'radial' | 'conic';
  angle?: number;
  author: {
    _id: string;
    username: string;
    avatar?: string;
  };
  tags: string[];
  category: string;
  stats: {
    views: number;
    likes: number;
    downloads: number;
    copies: number;
  };
  likedBy: string[];
  createdAt: string;
  updatedAt: string;
}

interface UseGradientsOptions {
  page?: number;
  limit?: number;
  category?: string;
  tags?: string[];
  search?: string;
  sortBy?: string;
  author?: string;
}

interface UseGradientsReturn {
  gradients: Gradient[];
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  } | null;
  refetch: () => void;
  loadMore: () => void;
}

export function useGradients(options: UseGradientsOptions = {}): UseGradientsReturn {
  const [gradients, setGradients] = useState<Gradient[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState(null);

  const fetchGradients = async (append = false) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (options.page) params.append('page', options.page.toString());
      if (options.limit) params.append('limit', options.limit.toString());
      if (options.category) params.append('category', options.category);
      if (options.tags?.length) params.append('tags', options.tags.join(','));
      if (options.search) params.append('search', options.search);
      if (options.sortBy) params.append('sortBy', options.sortBy);
      if (options.author) params.append('author', options.author);

      const response = await fetch(`/api/gradients?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch gradients');
      }

      const data = await response.json();
      
      if (append) {
        setGradients(prev => [...prev, ...data.gradients]);
      } else {
        setGradients(data.gradients);
      }
      
      setPagination(data.pagination);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const refetch = () => {
    fetchGradients(false);
  };

  const loadMore = () => {
    if (pagination?.hasNext) {
      const nextPage = pagination.page + 1;
      fetchGradients(true);
    }
  };

  useEffect(() => {
    fetchGradients();
  }, [
    options.page,
    options.limit,
    options.category,
    options.tags?.join(','),
    options.search,
    options.sortBy,
    options.author
  ]);

  return {
    gradients,
    loading,
    error,
    pagination,
    refetch,
    loadMore
  };
}

// Hook for single gradient
export function useGradient(id: string) {
  const [gradient, setGradient] = useState<Gradient | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) return;

    const fetchGradient = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/gradients/${id}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch gradient');
        }

        const data = await response.json();
        setGradient(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchGradient();
  }, [id]);

  return { gradient, loading, error };
}

// Hook for gradient actions
export function useGradientActions() {
  const [loading, setLoading] = useState(false);

  const likeGradient = async (gradientId: string, userId: string) => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/gradients/${gradientId}/like`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId })
      });

      if (!response.ok) {
        throw new Error('Failed to toggle like');
      }

      return await response.json();
    } catch (error) {
      console.error('Error toggling like:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const trackDownload = async (gradientId: string, format: string) => {
    try {
      const response = await fetch(`/api/gradients/${gradientId}/download`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ format })
      });

      if (!response.ok) {
        throw new Error('Failed to track download');
      }

      return await response.json();
    } catch (error) {
      console.error('Error tracking download:', error);
    }
  };

  const trackCopy = async (gradientId: string, format: string) => {
    try {
      const response = await fetch(`/api/gradients/${gradientId}/download`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ format })
      });

      if (!response.ok) {
        throw new Error('Failed to track copy');
      }

      return await response.json();
    } catch (error) {
      console.error('Error tracking copy:', error);
    }
  };

  const createGradient = async (gradientData: any) => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/gradients', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(gradientData)
      });

      if (!response.ok) {
        throw new Error('Failed to create gradient');
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating gradient:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    likeGradient,
    trackDownload,
    trackCopy,
    createGradient
  };
}
