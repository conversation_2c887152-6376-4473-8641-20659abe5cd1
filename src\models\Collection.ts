import mongoose, { Document, Schema } from 'mongoose';

export interface ICollection extends Document {
  _id: string;
  name: string;
  description: string;
  author: string; // User ID
  gradients: string[]; // Gradient IDs
  thumbnail: string[]; // Array of colors for preview
  tags: string[];
  isPublic: boolean;
  isFeatured: boolean;
  stats: {
    views: number;
    likes: number;
    saves: number;
    shares: number;
  };
  likedBy: string[]; // User IDs
  savedBy: string[]; // User IDs who saved this collection
  createdAt: Date;
  updatedAt: Date;
}

const CollectionSchema = new Schema<ICollection>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    required: true,
    maxlength: 500
  },
  author: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  gradients: [{
    type: Schema.Types.ObjectId,
    ref: 'Gradient'
  }],
  thumbnail: [{
    type: String,
    match: /^#[0-9A-Fa-f]{6}$/
  }],
  tags: [{
    type: String,
    lowercase: true,
    trim: true
  }],
  isPublic: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  stats: {
    views: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    saves: { type: Number, default: 0 },
    shares: { type: Number, default: 0 }
  },
  likedBy: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],
  savedBy: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
CollectionSchema.index({ author: 1, createdAt: -1 });
CollectionSchema.index({ isPublic: 1, isFeatured: 1 });
CollectionSchema.index({ tags: 1, isPublic: 1 });
CollectionSchema.index({ 'stats.likes': -1, isPublic: 1 });
CollectionSchema.index({ createdAt: -1, isPublic: 1 });

// Text search
CollectionSchema.index({
  name: 'text',
  description: 'text',
  tags: 'text'
});

// Virtuals
CollectionSchema.virtual('authorDetails', {
  ref: 'User',
  localField: 'author',
  foreignField: '_id',
  justOne: true
});

CollectionSchema.virtual('gradientDetails', {
  ref: 'Gradient',
  localField: 'gradients',
  foreignField: '_id'
});

CollectionSchema.virtual('gradientCount').get(function() {
  return this.gradients?.length || 0;
});

// Pre-save middleware
CollectionSchema.pre('save', function(next) {
  if (this.isModified('likedBy')) {
    this.stats.likes = this.likedBy?.length || 0;
  }
  if (this.isModified('savedBy')) {
    this.stats.saves = this.savedBy?.length || 0;
  }
  next();
});

export default mongoose.models.Collection || mongoose.model<ICollection>('Collection', CollectionSchema);
