"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Folder, 
  Plus, 
  Search, 
  Heart, 
  Eye, 
  Star,
  Grid3X3,
  List,
  Filter
} from "lucide-react";

interface Collection {
  id: string;
  name: string;
  description: string;
  gradientCount: number;
  likes: number;
  views: number;
  isPublic: boolean;
  author: string;
  thumbnail: string[];
  tags: string[];
  createdAt: string;
}

const sampleCollections: Collection[] = [
  {
    id: "1",
    name: "Sunset Vibes",
    description: "Warm and cozy gradients inspired by beautiful sunsets around the world",
    gradientCount: 24,
    likes: 1234,
    views: 5678,
    isPublic: true,
    author: "SunsetLover",
    thumbnail: ["#ff9a9e", "#fecfef", "#ffd89b"],
    tags: ["warm", "sunset", "orange", "pink"],
    createdAt: "2024-01-15"
  },
  {
    id: "2",
    name: "Ocean Depths",
    description: "Deep blue gradients that capture the mystery and beauty of the ocean",
    gradientCount: 18,
    likes: 987,
    views: 3456,
    isPublic: true,
    author: "OceanExplorer",
    thumbnail: ["#667eea", "#764ba2", "#00d2ff"],
    tags: ["blue", "ocean", "deep", "cool"],
    createdAt: "2024-01-10"
  },
  {
    id: "3",
    name: "Neon Dreams",
    description: "Vibrant neon gradients perfect for modern digital designs",
    gradientCount: 32,
    likes: 2156,
    views: 8901,
    isPublic: true,
    author: "NeonArtist",
    thumbnail: ["#ff0099", "#493240", "#00ff88"],
    tags: ["neon", "vibrant", "electric", "modern"],
    createdAt: "2024-01-20"
  },
  {
    id: "4",
    name: "Pastel Paradise",
    description: "Soft and gentle pastel gradients for delicate designs",
    gradientCount: 28,
    likes: 1567,
    views: 4321,
    isPublic: true,
    author: "PastelDreamer",
    thumbnail: ["#a8edea", "#fed6e3", "#d299c2"],
    tags: ["pastel", "soft", "gentle", "light"],
    createdAt: "2024-01-12"
  },
  {
    id: "5",
    name: "Space Odyssey",
    description: "Cosmic gradients inspired by the vastness of space",
    gradientCount: 21,
    likes: 1890,
    views: 6543,
    isPublic: true,
    author: "SpaceExplorer",
    thumbnail: ["#667eea", "#764ba2", "#f093fb"],
    tags: ["space", "cosmic", "purple", "galaxy"],
    createdAt: "2024-01-18"
  },
  {
    id: "6",
    name: "Forest Harmony",
    description: "Natural green gradients inspired by lush forests",
    gradientCount: 16,
    likes: 876,
    views: 2987,
    isPublic: true,
    author: "NatureLover",
    thumbnail: ["#11998e", "#38ef7d", "#56ab2f"],
    tags: ["green", "nature", "forest", "organic"],
    createdAt: "2024-01-08"
  }
];

export default function CollectionsContent() {
  const [collections, setCollections] = useState(sampleCollections);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState("popular");

  const filteredCollections = collections.filter(collection =>
    collection.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    collection.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    collection.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl md:text-4xl font-bold mb-4">
          <Folder className="inline h-8 w-8 mr-2 text-purple-500" />
          Gradient Collections
        </h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Discover curated collections of gradient color palettes organized by themes, styles, and moods. 
          Create your own collections and share them with the community.
        </p>
      </div>

      {/* Controls */}
      <div className="flex flex-col md:flex-row gap-4 mb-8">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search collections..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2">
          <Button
            variant={viewMode === "grid" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("grid")}
          >
            <Grid3X3 className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === "list" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("list")}
          >
            <List className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button size="sm" className="bg-gradient-to-r from-purple-500 to-pink-500">
            <Plus className="h-4 w-4 mr-2" />
            Create Collection
          </Button>
        </div>
      </div>

      {/* Collections Grid/List */}
      {viewMode === "grid" ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCollections.map((collection) => (
            <Card key={collection.id} className="group hover:shadow-lg transition-all duration-300 cursor-pointer">
              <CardHeader className="pb-3">
                {/* Thumbnail */}
                <div className="flex gap-1 mb-3">
                  {collection.thumbnail.map((color, index) => (
                    <div
                      key={index}
                      className="flex-1 h-20 rounded-lg"
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
                
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg truncate">{collection.name}</CardTitle>
                    <p className="text-sm text-muted-foreground">by {collection.author}</p>
                  </div>
                  <Badge variant="secondary">{collection.gradientCount}</Badge>
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                  {collection.description}
                </p>
                
                <div className="flex flex-wrap gap-1 mb-4">
                  {collection.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {collection.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{collection.tags.length - 3}
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <div className="flex items-center gap-3">
                    <span className="flex items-center gap-1">
                      <Heart className="h-3 w-3" />
                      {collection.likes}
                    </span>
                    <span className="flex items-center gap-1">
                      <Eye className="h-3 w-3" />
                      {collection.views}
                    </span>
                  </div>
                  <Button variant="ghost" size="sm" className="h-auto p-0">
                    View Collection
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredCollections.map((collection) => (
            <Card key={collection.id} className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  {/* Thumbnail */}
                  <div className="flex gap-1 w-24">
                    {collection.thumbnail.map((color, index) => (
                      <div
                        key={index}
                        className="flex-1 h-16 rounded"
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                  
                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h3 className="text-lg font-semibold">{collection.name}</h3>
                        <p className="text-sm text-muted-foreground">by {collection.author}</p>
                      </div>
                      <Badge variant="secondary">{collection.gradientCount} gradients</Badge>
                    </div>
                    
                    <p className="text-sm text-muted-foreground mb-3">
                      {collection.description}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex flex-wrap gap-1">
                        {collection.tags.slice(0, 4).map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <Heart className="h-3 w-3" />
                          {collection.likes}
                        </span>
                        <span className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          {collection.views}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Empty State */}
      {filteredCollections.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">📁</div>
          <h3 className="text-xl font-semibold mb-2">No collections found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your search or create a new collection.
          </p>
          <Button className="bg-gradient-to-r from-purple-500 to-pink-500">
            <Plus className="h-4 w-4 mr-2" />
            Create Your First Collection
          </Button>
        </div>
      )}
    </div>
  );
}
