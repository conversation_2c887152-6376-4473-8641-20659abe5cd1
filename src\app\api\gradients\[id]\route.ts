import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Gradient from '@/models/Gradient';
import User from '@/models/User';

// GET /api/gradients/[id] - Get single gradient
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();

    const gradient = await Gradient.findById(params.id)
      .populate('author', 'username avatar bio')
      .lean();

    if (!gradient) {
      return NextResponse.json(
        { error: 'Gradient not found' },
        { status: 404 }
      );
    }

    // Increment view count
    await Gradient.findByIdAndUpdate(params.id, {
      $inc: { 'stats.views': 1 }
    });

    return NextResponse.json(gradient);

  } catch (error) {
    console.error('Error fetching gradient:', error);
    return NextResponse.json(
      { error: 'Failed to fetch gradient' },
      { status: 500 }
    );
  }
}

// PUT /api/gradients/[id] - Update gradient
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();

    const body = await request.json();
    const { userId, ...updateData } = body;

    // Find gradient and check ownership
    const gradient = await Gradient.findById(params.id);
    if (!gradient) {
      return NextResponse.json(
        { error: 'Gradient not found' },
        { status: 404 }
      );
    }

    if (gradient.author.toString() !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Update gradient
    const updatedGradient = await Gradient.findByIdAndUpdate(
      params.id,
      updateData,
      { new: true, runValidators: true }
    ).populate('author', 'username avatar');

    return NextResponse.json(updatedGradient);

  } catch (error) {
    console.error('Error updating gradient:', error);
    return NextResponse.json(
      { error: 'Failed to update gradient' },
      { status: 500 }
    );
  }
}

// DELETE /api/gradients/[id] - Delete gradient
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID required' },
        { status: 400 }
      );
    }

    // Find gradient and check ownership
    const gradient = await Gradient.findById(params.id);
    if (!gradient) {
      return NextResponse.json(
        { error: 'Gradient not found' },
        { status: 404 }
      );
    }

    if (gradient.author.toString() !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Delete gradient
    await Gradient.findByIdAndDelete(params.id);

    // Update user stats
    await User.findByIdAndUpdate(userId, {
      $inc: { 'stats.gradientsCreated': -1 }
    });

    return NextResponse.json({ message: 'Gradient deleted successfully' });

  } catch (error) {
    console.error('Error deleting gradient:', error);
    return NextResponse.json(
      { error: 'Failed to delete gradient' },
      { status: 500 }
    );
  }
}
