"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { 
  Download, 
  Copy, 
  Eye, 
  Heart, 
  Palette,
  Box,
  Lightbulb,
  Layers,
  Check
} from "lucide-react";

interface BlenderPalette {
  id: string;
  name: string;
  description: string;
  colors: string[];
  category: string;
  downloads: number;
  likes: number;
  author: string;
  tags: string[];
  blenderFile?: string;
}

const blenderPalettes: BlenderPalette[] = [
  {
    id: "bp1",
    name: "Cyberpunk Neon",
    description: "Vibrant neon colors perfect for cyberpunk and futuristic scenes",
    colors: ["#ff0080", "#00ff80", "#8000ff", "#ff8000", "#0080ff", "#ff0040", "#40ff00", "#8040ff"],
    category: "Sci-Fi",
    downloads: 2340,
    likes: 567,
    author: "CyberArtist",
    tags: ["neon", "cyberpunk", "futuristic", "vibrant"]
  },
  {
    id: "bp2", 
    name: "Natural Earth Tones",
    description: "Organic earth colors for realistic nature and landscape renders",
    colors: ["#8B4513", "#A0522D", "#CD853F", "#DEB887", "#F4A460", "#D2691E", "#BC8F8F", "#F5DEB3"],
    category: "Nature",
    downloads: 1890,
    likes: 423,
    author: "NatureRenderer",
    tags: ["earth", "natural", "organic", "landscape"]
  },
  {
    id: "bp3",
    name: "Ocean Depths",
    description: "Deep ocean blues and teals for underwater and marine scenes",
    colors: ["#000080", "#191970", "#0000CD", "#0000FF", "#4169E1", "#1E90FF", "#00BFFF", "#87CEEB"],
    category: "Water",
    downloads: 1567,
    likes: 389,
    author: "OceanExplorer",
    tags: ["ocean", "water", "blue", "marine"]
  },
  {
    id: "bp4",
    name: "Sunset Warmth",
    description: "Warm sunset colors for dramatic lighting and atmospheric renders",
    colors: ["#FF4500", "#FF6347", "#FF7F50", "#FFA500", "#FFB347", "#FFCCCB", "#FFE4B5", "#FFF8DC"],
    category: "Lighting",
    downloads: 2156,
    likes: 634,
    author: "LightMaster",
    tags: ["sunset", "warm", "lighting", "atmospheric"]
  },
  {
    id: "bp5",
    name: "Metallic Surfaces",
    description: "Realistic metallic colors for industrial and mechanical objects",
    colors: ["#C0C0C0", "#A9A9A9", "#808080", "#696969", "#778899", "#708090", "#2F4F4F", "#000000"],
    category: "Materials",
    downloads: 1234,
    likes: 298,
    author: "MetalWorker",
    tags: ["metal", "industrial", "realistic", "materials"]
  },
  {
    id: "bp6",
    name: "Fantasy Magic",
    description: "Mystical and magical colors for fantasy and supernatural scenes",
    colors: ["#9400D3", "#8A2BE2", "#9932CC", "#BA55D3", "#DA70D6", "#EE82EE", "#DDA0DD", "#E6E6FA"],
    category: "Fantasy",
    downloads: 1789,
    likes: 456,
    author: "MagicRenderer",
    tags: ["fantasy", "magic", "mystical", "supernatural"]
  }
];

export default function BlenderPalettes() {
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [copiedPalette, setCopiedPalette] = useState<string | null>(null);

  const categories = ["all", "Sci-Fi", "Nature", "Water", "Lighting", "Materials", "Fantasy"];

  const filteredPalettes = selectedCategory === "all" 
    ? blenderPalettes 
    : blenderPalettes.filter(palette => palette.category === selectedCategory);

  const copyPalette = async (palette: BlenderPalette, format: "hex" | "rgb" | "blender") => {
    let text = "";
    
    switch (format) {
      case "hex":
        text = palette.colors.join(", ");
        break;
      case "rgb":
        text = palette.colors.map(hex => {
          const r = parseInt(hex.slice(1, 3), 16);
          const g = parseInt(hex.slice(3, 5), 16);
          const b = parseInt(hex.slice(5, 7), 16);
          return `rgb(${r}, ${g}, ${b})`;
        }).join(", ");
        break;
      case "blender":
        text = palette.colors.map(hex => {
          const r = (parseInt(hex.slice(1, 3), 16) / 255).toFixed(3);
          const g = (parseInt(hex.slice(3, 5), 16) / 255).toFixed(3);
          const b = (parseInt(hex.slice(5, 7), 16) / 255).toFixed(3);
          return `(${r}, ${g}, ${b}, 1.0)`;
        }).join("\n");
        break;
    }

    try {
      await navigator.clipboard.writeText(text);
      setCopiedPalette(palette.id + format);
      setTimeout(() => setCopiedPalette(null), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  return (
    <TooltipProvider>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">
            <Box className="inline h-8 w-8 mr-2 text-orange-500" />
            Blender Color Palettes
          </h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Professional color palettes optimized for Blender 3D. Each palette is carefully crafted 
            for specific use cases and comes with ready-to-use color values for your 3D projects.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex justify-center mb-8">
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
            <TabsList className="grid grid-cols-7 w-full max-w-3xl">
              {categories.map((category) => (
                <TabsTrigger key={category} value={category} className="capitalize">
                  {category}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </div>

        {/* Palettes Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {filteredPalettes.map((palette) => (
            <Card key={palette.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-xl mb-1">{palette.name}</CardTitle>
                    <p className="text-sm text-muted-foreground mb-2">by {palette.author}</p>
                    <p className="text-sm text-muted-foreground">{palette.description}</p>
                  </div>
                  <Badge variant="secondary">{palette.category}</Badge>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Color Grid */}
                <div className="grid grid-cols-8 gap-1 h-16 rounded-lg overflow-hidden">
                  {palette.colors.map((color, index) => (
                    <Tooltip key={index}>
                      <TooltipTrigger asChild>
                        <div
                          className="cursor-pointer hover:scale-105 transition-transform"
                          style={{ backgroundColor: color }}
                          onClick={() => navigator.clipboard.writeText(color)}
                        />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{color}</p>
                      </TooltipContent>
                    </Tooltip>
                  ))}
                </div>

                {/* Color Values */}
                <div className="space-y-2">
                  <Tabs defaultValue="hex" className="w-full">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="hex">HEX</TabsTrigger>
                      <TabsTrigger value="rgb">RGB</TabsTrigger>
                      <TabsTrigger value="blender">Blender</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="hex" className="mt-3">
                      <div className="bg-muted p-3 rounded-lg">
                        <div className="flex flex-wrap gap-1 text-xs font-mono">
                          {palette.colors.map((color, index) => (
                            <span key={index} className="bg-background px-2 py-1 rounded">
                              {color}
                            </span>
                          ))}
                        </div>
                      </div>
                      <Button
                        onClick={() => copyPalette(palette, "hex")}
                        variant="outline"
                        size="sm"
                        className="w-full mt-2"
                      >
                        {copiedPalette === palette.id + "hex" ? (
                          <><Check className="h-4 w-4 mr-2" /> Copied!</>
                        ) : (
                          <><Copy className="h-4 w-4 mr-2" /> Copy HEX Values</>
                        )}
                      </Button>
                    </TabsContent>

                    <TabsContent value="rgb" className="mt-3">
                      <div className="bg-muted p-3 rounded-lg">
                        <div className="space-y-1 text-xs font-mono">
                          {palette.colors.map((color, index) => {
                            const r = parseInt(color.slice(1, 3), 16);
                            const g = parseInt(color.slice(3, 5), 16);
                            const b = parseInt(color.slice(5, 7), 16);
                            return (
                              <div key={index} className="bg-background px-2 py-1 rounded">
                                rgb({r}, {g}, {b})
                              </div>
                            );
                          })}
                        </div>
                      </div>
                      <Button
                        onClick={() => copyPalette(palette, "rgb")}
                        variant="outline"
                        size="sm"
                        className="w-full mt-2"
                      >
                        {copiedPalette === palette.id + "rgb" ? (
                          <><Check className="h-4 w-4 mr-2" /> Copied!</>
                        ) : (
                          <><Copy className="h-4 w-4 mr-2" /> Copy RGB Values</>
                        )}
                      </Button>
                    </TabsContent>

                    <TabsContent value="blender" className="mt-3">
                      <div className="bg-muted p-3 rounded-lg">
                        <div className="space-y-1 text-xs font-mono">
                          {palette.colors.map((color, index) => {
                            const r = (parseInt(color.slice(1, 3), 16) / 255).toFixed(3);
                            const g = (parseInt(color.slice(3, 5), 16) / 255).toFixed(3);
                            const b = (parseInt(color.slice(5, 7), 16) / 255).toFixed(3);
                            return (
                              <div key={index} className="bg-background px-2 py-1 rounded">
                                ({r}, {g}, {b}, 1.0)
                              </div>
                            );
                          })}
                        </div>
                      </div>
                      <Button
                        onClick={() => copyPalette(palette, "blender")}
                        variant="outline"
                        size="sm"
                        className="w-full mt-2"
                      >
                        {copiedPalette === palette.id + "blender" ? (
                          <><Check className="h-4 w-4 mr-2" /> Copied!</>
                        ) : (
                          <><Copy className="h-4 w-4 mr-2" /> Copy Blender Values</>
                        )}
                      </Button>
                    </TabsContent>
                  </Tabs>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-1">
                  {palette.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>

                {/* Stats and Actions */}
                <div className="flex items-center justify-between pt-2 border-t">
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Download className="h-3 w-3" />
                      {palette.downloads}
                    </span>
                    <span className="flex items-center gap-1">
                      <Heart className="h-3 w-3" />
                      {palette.likes}
                    </span>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Download .blend
                    </Button>
                    <Button variant="outline" size="sm">
                      <Heart className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Usage Guide */}
        <Card className="mt-12">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Lightbulb className="h-5 w-5 mr-2" />
              How to Use in Blender
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Copy className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">1. Copy Values</h3>
                <p className="text-sm text-muted-foreground">
                  Copy the Blender-formatted color values from any palette above.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Palette className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">2. Open Material Editor</h3>
                <p className="text-sm text-muted-foreground">
                  In Blender, switch to Shading workspace and select your object.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Layers className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">3. Apply Colors</h3>
                <p className="text-sm text-muted-foreground">
                  Paste the values into ColorRamp nodes or Base Color inputs.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
}
