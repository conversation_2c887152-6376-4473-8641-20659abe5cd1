import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import Gradient from '@/models/Gradient';

// GET /api/users/[id] - Get user profile
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const includeGradients = searchParams.get('includeGradients') === 'true';

    // Find user
    const user = await User.findById(id)
      .select('-password -email')
      .lean();

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if profile is public
    if (!user.preferences.publicProfile) {
      return NextResponse.json(
        { error: 'Profile is private' },
        { status: 403 }
      );
    }

    let gradients = [];
    if (includeGradients) {
      gradients = await Gradient.find({
        author: id,
        isPublic: true
      })
      .select('name colors css tailwind stats tags category createdAt')
      .sort({ createdAt: -1 })
      .limit(12)
      .lean();
    }

    return NextResponse.json({
      user,
      gradients
    });

  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user' },
      { status: 500 }
    );
  }
}

// PUT /api/users/[id] - Update user profile
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();
    const { userId, ...updateData } = body;

    // Check if user is updating their own profile
    if (id !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Remove sensitive fields that shouldn't be updated via this endpoint
    delete updateData.password;
    delete updateData.email;
    delete updateData.role;
    delete updateData.stats;
    delete updateData.favoriteGradients;
    delete updateData.favoriteCollections;

    // Update user
    const updatedUser = await User.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).select('-password -email');

    if (!updatedUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedUser);

  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}
