import mongoose, { Document, Schema } from 'mongoose';

export interface IGradient extends Document {
  _id: string;
  name: string;
  description?: string;
  colors: string[];
  css: string;
  tailwind: string;
  type: 'linear' | 'radial' | 'conic';
  angle?: number;
  author: string; // User ID
  tags: string[];
  category: string;
  isPublic: boolean;
  isFeatured: boolean;
  stats: {
    views: number;
    likes: number;
    downloads: number;
    copies: number;
    shares: number;
  };
  likedBy: string[]; // User IDs
  collections: string[]; // Collection IDs
  metadata: {
    colorStops: Array<{
      color: string;
      position: number;
    }>;
    blenderColors?: Array<{
      r: number;
      g: number;
      b: number;
      a: number;
    }>;
    accessibility: {
      contrastRatio?: number;
      colorBlindSafe: boolean;
    };
  };
  createdAt: Date;
  updatedAt: Date;
}

const GradientSchema = new Schema<IGradient>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    maxlength: 500,
    default: ''
  },
  colors: [{
    type: String,
    required: true,
    match: /^#[0-9A-Fa-f]{6}$/
  }],
  css: {
    type: String,
    required: true
  },
  tailwind: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: ['linear', 'radial', 'conic'],
    default: 'linear'
  },
  angle: {
    type: Number,
    min: 0,
    max: 360,
    default: 135
  },
  author: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  tags: [{
    type: String,
    lowercase: true,
    trim: true
  }],
  category: {
    type: String,
    required: true,
    enum: [
      'warm', 'cool', 'neutral', 'pastel', 'vibrant', 'dark', 'light',
      'nature', 'space', 'ocean', 'sunset', 'neon', 'vintage', 'modern',
      'gradient', 'abstract', 'minimal', 'colorful'
    ]
  },
  isPublic: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  stats: {
    views: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    downloads: { type: Number, default: 0 },
    copies: { type: Number, default: 0 },
    shares: { type: Number, default: 0 }
  },
  likedBy: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],
  collections: [{
    type: Schema.Types.ObjectId,
    ref: 'Collection'
  }],
  metadata: {
    colorStops: [{
      color: {
        type: String,
        required: true,
        match: /^#[0-9A-Fa-f]{6}$/
      },
      position: {
        type: Number,
        required: true,
        min: 0,
        max: 100
      }
    }],
    blenderColors: [{
      r: { type: Number, min: 0, max: 1 },
      g: { type: Number, min: 0, max: 1 },
      b: { type: Number, min: 0, max: 1 },
      a: { type: Number, min: 0, max: 1, default: 1 }
    }],
    accessibility: {
      contrastRatio: { type: Number },
      colorBlindSafe: { type: Boolean, default: false }
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
GradientSchema.index({ author: 1, createdAt: -1 });
GradientSchema.index({ category: 1, isPublic: 1 });
GradientSchema.index({ tags: 1, isPublic: 1 });
GradientSchema.index({ 'stats.likes': -1, isPublic: 1 });
GradientSchema.index({ 'stats.views': -1, isPublic: 1 });
GradientSchema.index({ createdAt: -1, isPublic: 1 });
GradientSchema.index({ isFeatured: 1, isPublic: 1 });

// Text search index
GradientSchema.index({
  name: 'text',
  description: 'text',
  tags: 'text'
});

// Virtual for author details
GradientSchema.virtual('authorDetails', {
  ref: 'User',
  localField: 'author',
  foreignField: '_id',
  justOne: true
});

// Virtual for like count
GradientSchema.virtual('likeCount').get(function() {
  return this.likedBy?.length || 0;
});

// Pre-save middleware to update stats
GradientSchema.pre('save', function(next) {
  if (this.isModified('likedBy')) {
    this.stats.likes = this.likedBy?.length || 0;
  }
  next();
});

export default mongoose.models.Gradient || mongoose.model<IGradient>('Gradient', GradientSchema);
