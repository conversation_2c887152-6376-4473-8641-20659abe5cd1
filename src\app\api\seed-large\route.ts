import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Gradient from '@/models/Gradient';
import User from '@/models/User';
import Collection from '@/models/Collection';

// Large gradient dataset
const gradientData = [
  // Warm gradients
  { name: "Sunset Blaze", colors: ["#ff6b6b", "#feca57"], category: "warm", tags: ["sunset", "warm", "orange"] },
  { name: "Fire Storm", colors: ["#ff9a56", "#ff6b6b", "#c44569"], category: "warm", tags: ["fire", "red", "intense"] },
  { name: "Golden Hour", colors: ["#ffd89b", "#19547b"], category: "warm", tags: ["golden", "hour", "yellow"] },
  { name: "Peach Melba", colors: ["#ffb347", "#ffcc5c"], category: "warm", tags: ["peach", "soft", "cream"] },
  { name: "Coral Reef", colors: ["#ff7675", "#fd79a8"], category: "warm", tags: ["coral", "pink", "reef"] },
  { name: "Autumn Leaves", colors: ["#ff7675", "#fdcb6e", "#e17055"], category: "warm", tags: ["autumn", "leaves", "fall"] },
  { name: "Mango Tango", colors: ["#ff9ff3", "#f368e0"], category: "warm", tags: ["mango", "tropical", "vibrant"] },
  { name: "Desert Sand", colors: ["#f7b733", "#fc4a1a"], category: "warm", tags: ["desert", "sand", "hot"] },
  
  // Cool gradients
  { name: "Ocean Depths", colors: ["#667eea", "#764ba2"], category: "cool", tags: ["ocean", "deep", "blue"] },
  { name: "Arctic Ice", colors: ["#74b9ff", "#0984e3"], category: "cool", tags: ["arctic", "ice", "cold"] },
  { name: "Forest Mist", colors: ["#00b894", "#00cec9"], category: "cool", tags: ["forest", "mist", "green"] },
  { name: "Midnight Blue", colors: ["#2d3436", "#636e72"], category: "cool", tags: ["midnight", "dark", "blue"] },
  { name: "Emerald Dream", colors: ["#00b894", "#55a3ff"], category: "cool", tags: ["emerald", "dream", "green"] },
  { name: "Frozen Lake", colors: ["#74b9ff", "#a29bfe"], category: "cool", tags: ["frozen", "lake", "winter"] },
  { name: "Deep Sea", colors: ["#0984e3", "#6c5ce7"], category: "cool", tags: ["deep", "sea", "mysterious"] },
  { name: "Mountain Peak", colors: ["#636e72", "#b2bec3"], category: "cool", tags: ["mountain", "peak", "gray"] },
  
  // Pastel gradients
  { name: "Cotton Candy", colors: ["#ffecd2", "#fcb69f"], category: "pastel", tags: ["cotton", "candy", "sweet"] },
  { name: "Lavender Fields", colors: ["#e8cbc0", "#636fa4"], category: "pastel", tags: ["lavender", "fields", "purple"] },
  { name: "Mint Cream", colors: ["#a8edea", "#fed6e3"], category: "pastel", tags: ["mint", "cream", "soft"] },
  { name: "Baby Blue", colors: ["#a8e6cf", "#dcedc1"], category: "pastel", tags: ["baby", "blue", "gentle"] },
  { name: "Rose Quartz", colors: ["#fad0c4", "#ffd1ff"], category: "pastel", tags: ["rose", "quartz", "pink"] },
  { name: "Powder Blue", colors: ["#cfd9df", "#e2ebf0"], category: "pastel", tags: ["powder", "blue", "light"] },
  { name: "Peach Blossom", colors: ["#ffecd2", "#fcb69f"], category: "pastel", tags: ["peach", "blossom", "spring"] },
  { name: "Lilac Dream", colors: ["#e0c3fc", "#9bb5ff"], category: "pastel", tags: ["lilac", "dream", "purple"] },
  
  // Vibrant gradients
  { name: "Electric Pulse", colors: ["#ff0099", "#493240"], category: "vibrant", tags: ["electric", "pulse", "neon"] },
  { name: "Neon Nights", colors: ["#00ff88", "#00d4ff"], category: "vibrant", tags: ["neon", "nights", "bright"] },
  { name: "Rainbow Burst", colors: ["#ff006e", "#8338ec", "#3a86ff"], category: "vibrant", tags: ["rainbow", "burst", "colorful"] },
  { name: "Cyber Punk", colors: ["#ff0080", "#7928ca"], category: "vibrant", tags: ["cyber", "punk", "futuristic"] },
  { name: "Laser Show", colors: ["#ff006e", "#fb5607", "#ffbe0b"], category: "vibrant", tags: ["laser", "show", "bright"] },
  { name: "Neon Glow", colors: ["#39ff14", "#ff073a"], category: "vibrant", tags: ["neon", "glow", "electric"] },
  { name: "Acid Trip", colors: ["#ff006e", "#8338ec", "#3a86ff"], category: "vibrant", tags: ["acid", "psychedelic", "intense"] },
  { name: "Electric Storm", colors: ["#7209b7", "#480ca8"], category: "vibrant", tags: ["electric", "storm", "purple"] },
  
  // Dark gradients
  { name: "Shadow Realm", colors: ["#2c3e50", "#34495e"], category: "dark", tags: ["shadow", "realm", "dark"] },
  { name: "Midnight Storm", colors: ["#232526", "#414345"], category: "dark", tags: ["midnight", "storm", "black"] },
  { name: "Dark Matter", colors: ["#0c0c0c", "#2c2c2c"], category: "dark", tags: ["dark", "matter", "space"] },
  { name: "Obsidian", colors: ["#1a1a1a", "#4a4a4a"], category: "dark", tags: ["obsidian", "black", "stone"] },
  { name: "Coal Mine", colors: ["#2c3e50", "#bdc3c7"], category: "dark", tags: ["coal", "mine", "industrial"] },
  { name: "Charcoal", colors: ["#36454f", "#708090"], category: "dark", tags: ["charcoal", "gray", "matte"] },
  { name: "Eclipse", colors: ["#000000", "#434343"], category: "dark", tags: ["eclipse", "black", "mysterious"] },
  { name: "Void", colors: ["#0f0f0f", "#2a2a2a"], category: "dark", tags: ["void", "empty", "dark"] },
  
  // Light gradients
  { name: "Morning Dew", colors: ["#f7f7f7", "#e8e8e8"], category: "light", tags: ["morning", "dew", "fresh"] },
  { name: "Cloud Nine", colors: ["#ffffff", "#f0f0f0"], category: "light", tags: ["cloud", "nine", "white"] },
  { name: "Pearl White", colors: ["#f8f8ff", "#e6e6fa"], category: "light", tags: ["pearl", "white", "elegant"] },
  { name: "Vanilla Sky", colors: ["#fffbf0", "#f5f5dc"], category: "light", tags: ["vanilla", "sky", "cream"] },
  { name: "Snow Fall", colors: ["#ffffff", "#f0f8ff"], category: "light", tags: ["snow", "fall", "winter"] },
  { name: "Angel Wings", colors: ["#f8f8ff", "#ffffff"], category: "light", tags: ["angel", "wings", "pure"] },
  { name: "Moonbeam", colors: ["#f5f5f5", "#e0e0e0"], category: "light", tags: ["moon", "beam", "silver"] },
  { name: "Crystal Clear", colors: ["#ffffff", "#f0f0f0"], category: "light", tags: ["crystal", "clear", "transparent"] },
  
  // Nature gradients
  { name: "Forest Canopy", colors: ["#134e5e", "#71b280"], category: "nature", tags: ["forest", "canopy", "green"] },
  { name: "Jungle Vine", colors: ["#11998e", "#38ef7d"], category: "nature", tags: ["jungle", "vine", "tropical"] },
  { name: "Mountain Meadow", colors: ["#56ab2f", "#a8e6cf"], category: "nature", tags: ["mountain", "meadow", "grass"] },
  { name: "River Flow", colors: ["#00d2ff", "#3a7bd5"], category: "nature", tags: ["river", "flow", "water"] },
  { name: "Sunset Valley", colors: ["#ff9a9e", "#fecfef"], category: "nature", tags: ["sunset", "valley", "landscape"] },
  { name: "Desert Bloom", colors: ["#ff9a56", "#ff6b6b"], category: "nature", tags: ["desert", "bloom", "cactus"] },
  { name: "Ocean Wave", colors: ["#2193b0", "#6dd5ed"], category: "nature", tags: ["ocean", "wave", "blue"] },
  { name: "Autumn Forest", colors: ["#ff7e5f", "#feb47b"], category: "nature", tags: ["autumn", "forest", "orange"] },
  
  // Space gradients
  { name: "Galaxy Far Away", colors: ["#667eea", "#764ba2"], category: "space", tags: ["galaxy", "far", "cosmic"] },
  { name: "Nebula Cloud", colors: ["#ff006e", "#8338ec"], category: "space", tags: ["nebula", "cloud", "purple"] },
  { name: "Starlight", colors: ["#4facfe", "#00f2fe"], category: "space", tags: ["star", "light", "bright"] },
  { name: "Black Hole", colors: ["#000000", "#434343"], category: "space", tags: ["black", "hole", "dark"] },
  { name: "Solar Flare", colors: ["#ff9a56", "#ff6b6b"], category: "space", tags: ["solar", "flare", "sun"] },
  { name: "Cosmic Dust", colors: ["#667eea", "#764ba2"], category: "space", tags: ["cosmic", "dust", "particles"] },
  { name: "Milky Way", colors: ["#2c3e50", "#fd746c"], category: "space", tags: ["milky", "way", "galaxy"] },
  { name: "Aurora Borealis", colors: ["#00c9ff", "#92fe9d"], category: "space", tags: ["aurora", "borealis", "northern"] },
  
  // Ocean gradients
  { name: "Deep Pacific", colors: ["#667eea", "#764ba2"], category: "ocean", tags: ["deep", "pacific", "blue"] },
  { name: "Coral Garden", colors: ["#ff7675", "#fd79a8"], category: "ocean", tags: ["coral", "garden", "reef"] },
  { name: "Tropical Waters", colors: ["#00d2ff", "#3a7bd5"], category: "ocean", tags: ["tropical", "waters", "clear"] },
  { name: "Tidal Wave", colors: ["#2193b0", "#6dd5ed"], category: "ocean", tags: ["tidal", "wave", "powerful"] },
  { name: "Underwater Cave", colors: ["#134e5e", "#71b280"], category: "ocean", tags: ["underwater", "cave", "mysterious"] },
  { name: "Sea Foam", colors: ["#a8edea", "#fed6e3"], category: "ocean", tags: ["sea", "foam", "bubbles"] },
  { name: "Ocean Sunset", colors: ["#ff9a9e", "#fecfef"], category: "ocean", tags: ["ocean", "sunset", "romantic"] },
  { name: "Aqua Marine", colors: ["#00b894", "#00cec9"], category: "ocean", tags: ["aqua", "marine", "turquoise"] },
  
  // Sunset gradients
  { name: "Golden Sunset", colors: ["#ff9a56", "#ff6b6b"], category: "sunset", tags: ["golden", "sunset", "warm"] },
  { name: "Purple Twilight", colors: ["#667eea", "#764ba2"], category: "sunset", tags: ["purple", "twilight", "evening"] },
  { name: "Orange Horizon", colors: ["#ff7e5f", "#feb47b"], category: "sunset", tags: ["orange", "horizon", "sky"] },
  { name: "Pink Clouds", colors: ["#ff9a9e", "#fecfef"], category: "sunset", tags: ["pink", "clouds", "soft"] },
  { name: "Red Sky", colors: ["#ff6b6b", "#feca57"], category: "sunset", tags: ["red", "sky", "dramatic"] },
  { name: "Dusk Light", colors: ["#667eea", "#764ba2"], category: "sunset", tags: ["dusk", "light", "peaceful"] },
  { name: "Evening Glow", colors: ["#ff7e5f", "#feb47b"], category: "sunset", tags: ["evening", "glow", "warm"] },
  { name: "Sunset Beach", colors: ["#ff9a9e", "#fecfef"], category: "sunset", tags: ["sunset", "beach", "vacation"] },
  
  // Neon gradients
  { name: "Electric Blue", colors: ["#00d4ff", "#0099cc"], category: "neon", tags: ["electric", "blue", "bright"] },
  { name: "Hot Pink", colors: ["#ff0099", "#ff6b9d"], category: "neon", tags: ["hot", "pink", "vibrant"] },
  { name: "Lime Green", colors: ["#39ff14", "#32cd32"], category: "neon", tags: ["lime", "green", "electric"] },
  { name: "Cyber Orange", colors: ["#ff6600", "#ff9933"], category: "neon", tags: ["cyber", "orange", "digital"] },
  { name: "Neon Purple", colors: ["#8a2be2", "#da70d6"], category: "neon", tags: ["neon", "purple", "glow"] },
  { name: "Electric Yellow", colors: ["#ffff00", "#ffd700"], category: "neon", tags: ["electric", "yellow", "bright"] },
  { name: "Plasma Red", colors: ["#ff073a", "#ff6b6b"], category: "neon", tags: ["plasma", "red", "intense"] },
  { name: "Neon Mint", colors: ["#00ff88", "#7fffd4"], category: "neon", tags: ["neon", "mint", "fresh"] },
  
  // Vintage gradients
  { name: "Retro Sunset", colors: ["#ff7e5f", "#feb47b"], category: "vintage", tags: ["retro", "sunset", "80s"] },
  { name: "Old Paper", colors: ["#f4f1de", "#e07a5f"], category: "vintage", tags: ["old", "paper", "aged"] },
  { name: "Sepia Tone", colors: ["#ddb892", "#a0826d"], category: "vintage", tags: ["sepia", "tone", "classic"] },
  { name: "Vintage Rose", colors: ["#d4a574", "#e9c46a"], category: "vintage", tags: ["vintage", "rose", "antique"] },
  { name: "Faded Glory", colors: ["#8d5524", "#c68642"], category: "vintage", tags: ["faded", "glory", "worn"] },
  { name: "Rustic Charm", colors: ["#a0826d", "#e9c46a"], category: "vintage", tags: ["rustic", "charm", "country"] },
  { name: "Antique Gold", colors: ["#d4af37", "#ffd700"], category: "vintage", tags: ["antique", "gold", "luxury"] },
  { name: "Weathered Wood", colors: ["#8b4513", "#daa520"], category: "vintage", tags: ["weathered", "wood", "natural"] },
  
  // Modern gradients
  { name: "Minimal Gray", colors: ["#f8f9fa", "#e9ecef"], category: "modern", tags: ["minimal", "gray", "clean"] },
  { name: "Tech Blue", colors: ["#007bff", "#6610f2"], category: "modern", tags: ["tech", "blue", "digital"] },
  { name: "Corporate", colors: ["#495057", "#6c757d"], category: "modern", tags: ["corporate", "professional", "business"] },
  { name: "Sleek Black", colors: ["#212529", "#495057"], category: "modern", tags: ["sleek", "black", "elegant"] },
  { name: "Fresh White", colors: ["#ffffff", "#f8f9fa"], category: "modern", tags: ["fresh", "white", "pure"] },
  { name: "Modern Teal", colors: ["#20c997", "#17a2b8"], category: "modern", tags: ["modern", "teal", "contemporary"] },
  { name: "Digital Purple", colors: ["#6f42c1", "#e83e8c"], category: "modern", tags: ["digital", "purple", "tech"] },
  { name: "Clean Green", colors: ["#28a745", "#20c997"], category: "modern", tags: ["clean", "green", "fresh"] }
];

export async function POST() {
  try {
    await connectDB();
    
    // Clear existing data
    await Gradient.deleteMany({});
    await User.deleteMany({});
    await Collection.deleteMany({});
    
    // Create users
    const users = await User.insertMany([
      {
        username: 'gradientmaster',
        email: '<EMAIL>',
        password: 'hashedpassword123',
        bio: 'Professional gradient designer with 5+ years experience',
        stats: { gradientsCreated: 50, totalLikes: 1234, totalViews: 5678 }
      },
      {
        username: 'colorwizard',
        email: '<EMAIL>', 
        password: 'hashedpassword123',
        bio: 'Color theory enthusiast and digital artist',
        stats: { gradientsCreated: 35, totalLikes: 987, totalViews: 4321 }
      },
      {
        username: 'designpro',
        email: '<EMAIL>',
        password: 'hashedpassword123',
        bio: 'UI/UX designer specializing in modern color schemes',
        stats: { gradientsCreated: 40, totalLikes: 1567, totalViews: 6789 }
      },
      {
        username: 'neonartist',
        email: '<EMAIL>',
        password: 'hashedpassword123',
        bio: 'Neon and cyberpunk gradient specialist',
        stats: { gradientsCreated: 25, totalLikes: 789, totalViews: 3456 }
      },
      {
        username: 'naturelover',
        email: '<EMAIL>',
        password: 'hashedpassword123',
        bio: 'Creating gradients inspired by nature',
        stats: { gradientsCreated: 30, totalLikes: 654, totalViews: 2987 }
      }
    ]);

    // Generate gradients
    const gradients = [];
    for (let i = 0; i < gradientData.length; i++) {
      const data = gradientData[i];
      const randomUser = users[Math.floor(Math.random() * users.length)];
      
      // Generate CSS
      const css = `linear-gradient(135deg, ${data.colors.join(', ')})`;
      
      // Generate Tailwind classes
      const tailwindColors = data.colors.map(color => {
        // Simple color mapping for demo
        if (color.includes('ff')) return 'red';
        if (color.includes('00')) return 'blue';
        if (color.includes('0f')) return 'green';
        return 'purple';
      });
      const tailwind = `bg-gradient-to-br from-${tailwindColors[0]}-400 to-${tailwindColors[tailwindColors.length - 1]}-600`;
      
      // Generate Blender colors
      const blenderColors = data.colors.map(color => {
        const r = parseInt(color.slice(1, 3), 16) / 255;
        const g = parseInt(color.slice(3, 5), 16) / 255;
        const b = parseInt(color.slice(5, 7), 16) / 255;
        return { r, g, b, a: 1 };
      });
      
      // Random stats
      const views = Math.floor(Math.random() * 1000) + 50;
      const likes = Math.floor(Math.random() * 100) + 5;
      const downloads = Math.floor(Math.random() * 50) + 1;
      const copies = Math.floor(Math.random() * 30) + 1;
      
      gradients.push({
        name: data.name,
        description: `Beautiful ${data.category} gradient with ${data.colors.length} colors`,
        colors: data.colors,
        css,
        tailwind,
        type: 'linear',
        angle: 135,
        author: randomUser._id,
        tags: data.tags,
        category: data.category,
        stats: { views, likes, downloads, copies },
        metadata: {
          colorStops: data.colors.map((color, index) => ({
            color,
            position: (index / (data.colors.length - 1)) * 100
          })),
          blenderColors,
          accessibility: { colorBlindSafe: Math.random() > 0.5 }
        }
      });
    }
    
    const savedGradients = await Gradient.insertMany(gradients);

    // Create collections
    const collections = [];
    const categories = ['warm', 'cool', 'pastel', 'vibrant', 'nature', 'space', 'ocean', 'sunset', 'neon', 'vintage'];
    
    for (const category of categories) {
      const categoryGradients = savedGradients.filter(g => g.category === category);
      if (categoryGradients.length > 0) {
        const randomUser = users[Math.floor(Math.random() * users.length)];
        const selectedGradients = categoryGradients.slice(0, Math.min(8, categoryGradients.length));
        
        collections.push({
          name: `${category.charAt(0).toUpperCase() + category.slice(1)} Collection`,
          description: `Curated collection of ${category} gradients`,
          author: randomUser._id,
          gradients: selectedGradients.map(g => g._id),
          thumbnail: selectedGradients.slice(0, 3).flatMap(g => g.colors.slice(0, 2)),
          tags: [category, 'curated', 'collection'],
          stats: {
            views: Math.floor(Math.random() * 500) + 50,
            likes: Math.floor(Math.random() * 50) + 5,
            saves: Math.floor(Math.random() * 25) + 2
          }
        });
      }
    }
    
    await Collection.insertMany(collections);

    return NextResponse.json({
      message: 'Large dataset seeded successfully!',
      data: {
        users: users.length,
        gradients: savedGradients.length,
        collections: collections.length
      }
    });

  } catch (error) {
    console.error('Error seeding large dataset:', error);
    return NextResponse.json(
      { 
        error: 'Failed to seed large dataset',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
