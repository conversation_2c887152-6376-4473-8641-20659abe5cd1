import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Gradient from '@/models/Gradient';
import User from '@/models/User';
import Collection from '@/models/Collection';

// GET /api/stats - Get platform statistics
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || 'all'; // 'day', 'week', 'month', 'year', 'all'

    // Calculate date range
    let dateFilter = {};
    const now = new Date();
    
    switch (period) {
      case 'day':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 24 * 60 * 60 * 1000) } };
        break;
      case 'week':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) } };
        break;
      case 'month':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) } };
        break;
      case 'year':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000) } };
        break;
    }

    // Get basic counts
    const [
      totalGradients,
      totalUsers,
      totalCollections,
      newGradients,
      newUsers,
      newCollections
    ] = await Promise.all([
      Gradient.countDocuments({ isPublic: true }),
      User.countDocuments(),
      Collection.countDocuments({ isPublic: true }),
      Gradient.countDocuments({ isPublic: true, ...dateFilter }),
      User.countDocuments(dateFilter),
      Collection.countDocuments({ isPublic: true, ...dateFilter })
    ]);

    // Get aggregated stats
    const [gradientStats, userStats] = await Promise.all([
      Gradient.aggregate([
        { $match: { isPublic: true } },
        {
          $group: {
            _id: null,
            totalViews: { $sum: '$stats.views' },
            totalLikes: { $sum: '$stats.likes' },
            totalDownloads: { $sum: '$stats.downloads' },
            totalCopies: { $sum: '$stats.copies' }
          }
        }
      ]),
      User.aggregate([
        {
          $group: {
            _id: null,
            totalUserLikes: { $sum: '$stats.totalLikes' },
            totalUserViews: { $sum: '$stats.totalViews' }
          }
        }
      ])
    ]);

    // Get top categories
    const topCategories = await Gradient.aggregate([
      { $match: { isPublic: true } },
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    // Get top tags
    const topTags = await Gradient.aggregate([
      { $match: { isPublic: true } },
      { $unwind: '$tags' },
      { $group: { _id: '$tags', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 20 }
    ]);

    // Get trending gradients (high views/likes ratio in recent period)
    const trendingGradients = await Gradient.find({
      isPublic: true,
      createdAt: { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) }
    })
    .sort({ 'stats.views': -1, 'stats.likes': -1 })
    .limit(5)
    .populate('author', 'username avatar')
    .select('name colors stats author createdAt')
    .lean();

    // Get top creators
    const topCreators = await User.find()
      .sort({ 'stats.totalLikes': -1 })
      .limit(10)
      .select('username avatar stats')
      .lean();

    const stats = {
      overview: {
        totalGradients,
        totalUsers,
        totalCollections,
        totalViews: gradientStats[0]?.totalViews || 0,
        totalLikes: gradientStats[0]?.totalLikes || 0,
        totalDownloads: gradientStats[0]?.totalDownloads || 0,
        totalCopies: gradientStats[0]?.totalCopies || 0
      },
      period: {
        name: period,
        newGradients,
        newUsers,
        newCollections
      },
      topCategories,
      topTags,
      trendingGradients,
      topCreators,
      growth: {
        gradientsGrowth: period !== 'all' ? ((newGradients / Math.max(totalGradients - newGradients, 1)) * 100).toFixed(1) : '0',
        usersGrowth: period !== 'all' ? ((newUsers / Math.max(totalUsers - newUsers, 1)) * 100).toFixed(1) : '0',
        collectionsGrowth: period !== 'all' ? ((newCollections / Math.max(totalCollections - newCollections, 1)) * 100).toFixed(1) : '0'
      }
    };

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Error fetching stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch stats' },
      { status: 500 }
    );
  }
}
