import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Pa<PERSON>, 
  Github, 
  Twitter, 
  Instagram, 
  Heart 
} from "lucide-react";

export default function Footer() {
  return (
    <footer className="border-t bg-background">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <Link href="/" className="flex items-center space-x-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-purple-500 to-pink-500">
                <Palette className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Gradient Hunt
              </span>
            </Link>
            <p className="text-sm text-muted-foreground max-w-xs">
              Discover and create beautiful gradient color palettes for your design projects.
            </p>
            <div className="flex space-x-2">
              <Button variant="ghost" size="sm">
                <Github className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Twitter className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Instagram className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Explore */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold">Explore</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/trending" className="text-muted-foreground hover:text-foreground transition-colors">
                  Trending
                </Link>
              </li>
              <li>
                <Link href="/popular" className="text-muted-foreground hover:text-foreground transition-colors">
                  Popular
                </Link>
              </li>
              <li>
                <Link href="/new" className="text-muted-foreground hover:text-foreground transition-colors">
                  New
                </Link>
              </li>
              <li>
                <Link href="/random" className="text-muted-foreground hover:text-foreground transition-colors">
                  Random
                </Link>
              </li>
            </ul>
          </div>

          {/* Categories */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold">Categories</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/gradients/warm" className="text-muted-foreground hover:text-foreground transition-colors">
                  Warm
                </Link>
              </li>
              <li>
                <Link href="/gradients/cool" className="text-muted-foreground hover:text-foreground transition-colors">
                  Cool
                </Link>
              </li>
              <li>
                <Link href="/gradients/pastel" className="text-muted-foreground hover:text-foreground transition-colors">
                  Pastel
                </Link>
              </li>
              <li>
                <Link href="/gradients/vibrant" className="text-muted-foreground hover:text-foreground transition-colors">
                  Vibrant
                </Link>
              </li>
            </ul>
          </div>

          {/* Tools */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold">Tools</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/create" className="text-muted-foreground hover:text-foreground transition-colors">
                  Gradient Generator
                </Link>
              </li>
              <li>
                <Link href="/color-picker" className="text-muted-foreground hover:text-foreground transition-colors">
                  Color Picker
                </Link>
              </li>
              <li>
                <Link href="/css-generator" className="text-muted-foreground hover:text-foreground transition-colors">
                  CSS Generator
                </Link>
              </li>
              <li>
                <Link href="/api" className="text-muted-foreground hover:text-foreground transition-colors">
                  API
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-muted-foreground">
            © 2024 Gradient Hunt. Made with{" "}
            <Heart className="inline h-4 w-4 text-red-500" />{" "}
            for designers.
          </p>
          <div className="flex space-x-4 mt-4 md:mt-0">
            <Link href="/privacy" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              Privacy Policy
            </Link>
            <Link href="/terms" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              Terms of Service
            </Link>
            <Link href="/about" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
              About
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
