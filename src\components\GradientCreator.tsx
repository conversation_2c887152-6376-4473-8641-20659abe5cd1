"use client";

import { useState, useCallback } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Plus, 
  Minus, 
  Copy, 
  Download, 
  Shuffle, 
  RotateCc<PERSON>,
  <PERSON><PERSON>,
  Check,
  Save
} from "lucide-react";

interface ColorStop {
  id: string;
  color: string;
  position: number;
}

export default function GradientCreator() {
  const [colorStops, setColorStops] = useState<ColorStop[]>([
    { id: "1", color: "#ff6b6b", position: 0 },
    { id: "2", color: "#4ecdc4", position: 100 }
  ]);
  
  const [gradientType, setGradientType] = useState<"linear" | "radial" | "conic">("linear");
  const [angle, setAngle] = useState(135);
  const [gradientName, setGradientName] = useState("My Gradient");
  const [copiedFormat, setCopiedFormat] = useState<string | null>(null);

  const generateCSS = useCallback(() => {
    const stops = colorStops
      .sort((a, b) => a.position - b.position)
      .map(stop => `${stop.color} ${stop.position}%`)
      .join(", ");

    switch (gradientType) {
      case "linear":
        return `linear-gradient(${angle}deg, ${stops})`;
      case "radial":
        return `radial-gradient(circle, ${stops})`;
      case "conic":
        return `conic-gradient(from ${angle}deg, ${stops})`;
      default:
        return `linear-gradient(${angle}deg, ${stops})`;
    }
  }, [colorStops, gradientType, angle]);

  const generateTailwind = useCallback(() => {
    // Simplified Tailwind generation - in a real app, this would be more sophisticated
    const direction = angle <= 45 || angle > 315 ? "to-r" : 
                     angle <= 135 ? "to-br" : 
                     angle <= 225 ? "to-b" : "to-bl";
    
    return `bg-gradient-${direction} from-${colorStops[0]?.color.replace("#", "")} to-${colorStops[colorStops.length - 1]?.color.replace("#", "")}`;
  }, [colorStops, angle]);

  const addColorStop = () => {
    const newPosition = colorStops.length > 0 
      ? Math.max(...colorStops.map(s => s.position)) + 10 
      : 50;
    
    const newStop: ColorStop = {
      id: Date.now().toString(),
      color: "#" + Math.floor(Math.random()*16777215).toString(16),
      position: Math.min(newPosition, 100)
    };
    
    setColorStops([...colorStops, newStop]);
  };

  const removeColorStop = (id: string) => {
    if (colorStops.length > 2) {
      setColorStops(colorStops.filter(stop => stop.id !== id));
    }
  };

  const updateColorStop = (id: string, updates: Partial<ColorStop>) => {
    setColorStops(colorStops.map(stop => 
      stop.id === id ? { ...stop, ...updates } : stop
    ));
  };

  const randomizeGradient = () => {
    const randomColors = Array.from({ length: Math.floor(Math.random() * 3) + 2 }, (_, i) => ({
      id: Date.now().toString() + i,
      color: "#" + Math.floor(Math.random()*16777215).toString(16),
      position: (i / (Math.floor(Math.random() * 3) + 1)) * 100
    }));
    
    setColorStops(randomColors);
    setAngle(Math.floor(Math.random() * 360));
  };

  const copyToClipboard = async (text: string, format: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedFormat(format);
      setTimeout(() => setCopiedFormat(null), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const css = generateCSS();
  const tailwind = generateTailwind();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">
            Create Your{" "}
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              Perfect Gradient
            </span>
          </h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Design beautiful gradients with our intuitive tools. Adjust colors, positions, and angles to create the perfect gradient for your project.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Preview */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5" />
                  Preview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div 
                  className="w-full h-64 rounded-lg border"
                  style={{ background: css }}
                />
                
                <div className="mt-4 space-y-3">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Gradient Name</label>
                    <Input
                      value={gradientName}
                      onChange={(e) => setGradientName(e.target.value)}
                      placeholder="Enter gradient name"
                    />
                  </div>
                  
                  <div className="flex gap-2">
                    <Button onClick={randomizeGradient} variant="outline" className="flex-1">
                      <Shuffle className="h-4 w-4 mr-2" />
                      Random
                    </Button>
                    <Button variant="outline" className="flex-1">
                      <Save className="h-4 w-4 mr-2" />
                      Save
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Export */}
            <Card>
              <CardHeader>
                <CardTitle>Export</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Tabs defaultValue="css">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="css">CSS</TabsTrigger>
                    <TabsTrigger value="tailwind">Tailwind</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="css" className="space-y-2">
                    <div className="bg-muted p-3 rounded-lg font-mono text-sm break-all">
                      background: {css};
                    </div>
                    <Button 
                      onClick={() => copyToClipboard(`background: ${css};`, 'css')}
                      className="w-full"
                      variant="outline"
                    >
                      {copiedFormat === 'css' ? (
                        <><Check className="h-4 w-4 mr-2" /> Copied!</>
                      ) : (
                        <><Copy className="h-4 w-4 mr-2" /> Copy CSS</>
                      )}
                    </Button>
                  </TabsContent>
                  
                  <TabsContent value="tailwind" className="space-y-2">
                    <div className="bg-muted p-3 rounded-lg font-mono text-sm break-all">
                      {tailwind}
                    </div>
                    <Button 
                      onClick={() => copyToClipboard(tailwind, 'tailwind')}
                      className="w-full"
                      variant="outline"
                    >
                      {copiedFormat === 'tailwind' ? (
                        <><Check className="h-4 w-4 mr-2" /> Copied!</>
                      ) : (
                        <><Copy className="h-4 w-4 mr-2" /> Copy Tailwind</>
                      )}
                    </Button>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Controls */}
          <div className="space-y-6">
            {/* Gradient Type */}
            <Card>
              <CardHeader>
                <CardTitle>Gradient Type</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs value={gradientType} onValueChange={(value) => setGradientType(value as any)}>
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="linear">Linear</TabsTrigger>
                    <TabsTrigger value="radial">Radial</TabsTrigger>
                    <TabsTrigger value="conic">Conic</TabsTrigger>
                  </TabsList>
                </Tabs>
                
                {(gradientType === "linear" || gradientType === "conic") && (
                  <div className="mt-4">
                    <label className="text-sm font-medium mb-2 block">
                      Angle: {angle}°
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="360"
                      value={angle}
                      onChange={(e) => setAngle(Number(e.target.value))}
                      className="w-full"
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Color Stops */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Color Stops
                  <Button onClick={addColorStop} size="sm" variant="outline">
                    <Plus className="h-4 w-4" />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {colorStops.map((stop) => (
                  <div key={stop.id} className="flex items-center gap-3">
                    <input
                      type="color"
                      value={stop.color}
                      onChange={(e) => updateColorStop(stop.id, { color: e.target.value })}
                      className="w-12 h-10 rounded border cursor-pointer"
                    />
                    <Input
                      value={stop.color}
                      onChange={(e) => updateColorStop(stop.id, { color: e.target.value })}
                      className="flex-1"
                    />
                    <div className="flex items-center gap-2">
                      <input
                        type="range"
                        min="0"
                        max="100"
                        value={stop.position}
                        onChange={(e) => updateColorStop(stop.id, { position: Number(e.target.value) })}
                        className="w-20"
                      />
                      <span className="text-sm w-10">{stop.position}%</span>
                    </div>
                    {colorStops.length > 2 && (
                      <Button
                        onClick={() => removeColorStop(stop.id)}
                        size="sm"
                        variant="outline"
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
