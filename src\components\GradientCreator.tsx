"use client";

import { useState, useCallback } from "react";
import { <PERSON>, <PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { useGradientActions } from "@/hooks/useGradients";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Plus,
  Minus,
  Copy,
  Download,
  Shuffle,
  RotateCcw,
  Palette,
  Check,
  Save,
} from "lucide-react";

interface ColorStop {
  id: string;
  color: string;
  position: number;
}

export default function GradientCreator() {
  const [colorStops, setColorStops] = useState<ColorStop[]>([
    { id: "1", color: "#ff6b6b", position: 0 },
    { id: "2", color: "#4ecdc4", position: 100 },
  ]);

  const [gradientType, setGradientType] = useState<
    "linear" | "radial" | "conic"
  >("linear");
  const [angle, setAngle] = useState(135);
  const [gradientName, setGradientName] = useState("My Gradient");
  const [description, setDescription] = useState("");
  const [tags, setTags] = useState<string[]>([]);
  const [category, setCategory] = useState("gradient");
  const [newTag, setNewTag] = useState("");
  const [isPublic, setIsPublic] = useState(true);
  const [copiedFormat, setCopiedFormat] = useState<string | null>(null);
  const { createGradient, loading } = useGradientActions();

  const generateCSS = useCallback(() => {
    const stops = colorStops
      .sort((a, b) => a.position - b.position)
      .map((stop) => `${stop.color} ${stop.position}%`)
      .join(", ");

    switch (gradientType) {
      case "linear":
        return `linear-gradient(${angle}deg, ${stops})`;
      case "radial":
        return `radial-gradient(circle, ${stops})`;
      case "conic":
        return `conic-gradient(from ${angle}deg, ${stops})`;
      default:
        return `linear-gradient(${angle}deg, ${stops})`;
    }
  }, [colorStops, gradientType, angle]);

  const generateTailwind = useCallback(() => {
    // Simplified Tailwind generation - in a real app, this would be more sophisticated
    const direction =
      angle <= 45 || angle > 315
        ? "to-r"
        : angle <= 135
        ? "to-br"
        : angle <= 225
        ? "to-b"
        : "to-bl";

    return `bg-gradient-${direction} from-${colorStops[0]?.color.replace(
      "#",
      ""
    )} to-${colorStops[colorStops.length - 1]?.color.replace("#", "")}`;
  }, [colorStops, angle]);

  const addColorStop = () => {
    const newPosition =
      colorStops.length > 0
        ? Math.max(...colorStops.map((s) => s.position)) + 10
        : 50;

    const newStop: ColorStop = {
      id: Date.now().toString(),
      color: "#" + Math.floor(Math.random() * 16777215).toString(16),
      position: Math.min(newPosition, 100),
    };

    setColorStops([...colorStops, newStop]);
  };

  const removeColorStop = (id: string) => {
    if (colorStops.length > 2) {
      setColorStops(colorStops.filter((stop) => stop.id !== id));
    }
  };

  const updateColorStop = (id: string, updates: Partial<ColorStop>) => {
    setColorStops(
      colorStops.map((stop) =>
        stop.id === id ? { ...stop, ...updates } : stop
      )
    );
  };

  const randomizeGradient = () => {
    const randomColors = Array.from(
      { length: Math.floor(Math.random() * 3) + 2 },
      (_, i) => ({
        id: Date.now().toString() + i,
        color: "#" + Math.floor(Math.random() * 16777215).toString(16),
        position: (i / (Math.floor(Math.random() * 3) + 1)) * 100,
      })
    );

    setColorStops(randomColors);
    setAngle(Math.floor(Math.random() * 360));
  };

  const copyToClipboard = async (text: string, format: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedFormat(format);
      setTimeout(() => setCopiedFormat(null), 2000);
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };

  const saveGradient = async () => {
    if (!gradientName.trim()) {
      alert("Please enter a gradient name");
      return;
    }

    try {
      const gradientData = {
        name: gradientName,
        description,
        colors: colorStops.map((stop) => stop.color),
        css: generateCSS(),
        tailwind: generateTailwind(),
        type: gradientType,
        angle,
        author: "687b5505d08ca683de524f90", // gradientmaster user ID
        tags,
        category,
        isPublic,
        colorStops: colorStops.map((stop) => ({
          color: stop.color,
          position: stop.position,
        })),
      };

      const result = await createGradient(gradientData);
      console.log("Gradient created successfully:", result);
      alert("Gradient saved successfully!");

      // Reset form
      setGradientName("My Gradient");
      setDescription("");
      setTags([]);
      setCategory("gradient");
    } catch (error) {
      console.error("Error saving gradient:", error);

      // More detailed error handling
      let errorMessage = "Failed to save gradient. ";
      if (error instanceof Error) {
        errorMessage += error.message;
      } else {
        errorMessage += "Please try again.";
      }

      alert(errorMessage);
    }
  };

  const css = generateCSS();
  const tailwind = generateTailwind();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">
            Create Your{" "}
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              Perfect Gradient
            </span>
          </h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Design beautiful gradients with our intuitive tools. Adjust colors,
            positions, and angles to create the perfect gradient for your
            project.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Preview */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5" />
                  Preview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div
                  className="w-full h-64 rounded-lg border"
                  style={{ background: css }}
                />

                <div className="mt-4 space-y-3">
                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      Gradient Name
                    </label>
                    <Input
                      value={gradientName}
                      onChange={(e) => setGradientName(e.target.value)}
                      placeholder="Enter gradient name"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      Description
                    </label>
                    <Input
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      placeholder="Describe your gradient"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      Category
                    </label>
                    <select
                      value={category}
                      onChange={(e) => setCategory(e.target.value)}
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="warm">Warm</option>
                      <option value="cool">Cool</option>
                      <option value="neutral">Neutral</option>
                      <option value="pastel">Pastel</option>
                      <option value="vibrant">Vibrant</option>
                      <option value="dark">Dark</option>
                      <option value="light">Light</option>
                      <option value="nature">Nature</option>
                      <option value="space">Space</option>
                      <option value="ocean">Ocean</option>
                      <option value="sunset">Sunset</option>
                      <option value="neon">Neon</option>
                      <option value="vintage">Vintage</option>
                      <option value="modern">Modern</option>
                      <option value="gradient">Gradient</option>
                      <option value="abstract">Abstract</option>
                      <option value="minimal">Minimal</option>
                      <option value="colorful">Colorful</option>
                    </select>
                  </div>

                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      Tags
                    </label>
                    <div className="flex gap-2 mb-2">
                      <Input
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        placeholder="Add a tag"
                        onKeyPress={(e) => e.key === "Enter" && addTag()}
                      />
                      <Button onClick={addTag} variant="outline" size="sm">
                        Add
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {tags.map((tag) => (
                        <Badge
                          key={tag}
                          variant="secondary"
                          className="cursor-pointer"
                          onClick={() => removeTag(tag)}
                        >
                          {tag} ×
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id="isPublic"
                      checked={isPublic}
                      onChange={(e) => setIsPublic(e.target.checked)}
                    />
                    <label htmlFor="isPublic" className="text-sm">
                      Make this gradient public
                    </label>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      onClick={randomizeGradient}
                      variant="outline"
                      className="flex-1"
                    >
                      <Shuffle className="h-4 w-4 mr-2" />
                      Random
                    </Button>
                    <Button
                      onClick={saveGradient}
                      disabled={loading}
                      variant="default"
                      className="flex-1"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      {loading ? "Saving..." : "Save"}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Export */}
            <Card>
              <CardHeader>
                <CardTitle>Export</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Tabs defaultValue="css">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="css">CSS</TabsTrigger>
                    <TabsTrigger value="tailwind">Tailwind</TabsTrigger>
                  </TabsList>

                  <TabsContent value="css" className="space-y-2">
                    <div className="bg-muted p-3 rounded-lg font-mono text-sm break-all">
                      background: {css};
                    </div>
                    <Button
                      onClick={() =>
                        copyToClipboard(`background: ${css};`, "css")
                      }
                      className="w-full"
                      variant="outline"
                    >
                      {copiedFormat === "css" ? (
                        <>
                          <Check className="h-4 w-4 mr-2" /> Copied!
                        </>
                      ) : (
                        <>
                          <Copy className="h-4 w-4 mr-2" /> Copy CSS
                        </>
                      )}
                    </Button>
                  </TabsContent>

                  <TabsContent value="tailwind" className="space-y-2">
                    <div className="bg-muted p-3 rounded-lg font-mono text-sm break-all">
                      {tailwind}
                    </div>
                    <Button
                      onClick={() => copyToClipboard(tailwind, "tailwind")}
                      className="w-full"
                      variant="outline"
                    >
                      {copiedFormat === "tailwind" ? (
                        <>
                          <Check className="h-4 w-4 mr-2" /> Copied!
                        </>
                      ) : (
                        <>
                          <Copy className="h-4 w-4 mr-2" /> Copy Tailwind
                        </>
                      )}
                    </Button>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Controls */}
          <div className="space-y-6">
            {/* Gradient Type */}
            <Card>
              <CardHeader>
                <CardTitle>Gradient Type</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs
                  value={gradientType}
                  onValueChange={(value) => setGradientType(value as any)}
                >
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="linear">Linear</TabsTrigger>
                    <TabsTrigger value="radial">Radial</TabsTrigger>
                    <TabsTrigger value="conic">Conic</TabsTrigger>
                  </TabsList>
                </Tabs>

                {(gradientType === "linear" || gradientType === "conic") && (
                  <div className="mt-4">
                    <label className="text-sm font-medium mb-2 block">
                      Angle: {angle}°
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="360"
                      value={angle}
                      onChange={(e) => setAngle(Number(e.target.value))}
                      className="w-full"
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Color Stops */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Color Stops
                  <Button onClick={addColorStop} size="sm" variant="outline">
                    <Plus className="h-4 w-4" />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {colorStops.map((stop) => (
                  <div key={stop.id} className="flex items-center gap-3">
                    <input
                      type="color"
                      value={stop.color}
                      onChange={(e) =>
                        updateColorStop(stop.id, { color: e.target.value })
                      }
                      className="w-12 h-10 rounded border cursor-pointer"
                    />
                    <Input
                      value={stop.color}
                      onChange={(e) =>
                        updateColorStop(stop.id, { color: e.target.value })
                      }
                      className="flex-1"
                    />
                    <div className="flex items-center gap-2">
                      <input
                        type="range"
                        min="0"
                        max="100"
                        value={stop.position}
                        onChange={(e) =>
                          updateColorStop(stop.id, {
                            position: Number(e.target.value),
                          })
                        }
                        className="w-20"
                      />
                      <span className="text-sm w-10">{stop.position}%</span>
                    </div>
                    {colorStops.length > 2 && (
                      <Button
                        onClick={() => removeColorStop(stop.id)}
                        size="sm"
                        variant="outline"
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
