"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Sparkles, Palette, TrendingUp, Users } from "lucide-react";
import Link from "next/link";

export default function HeroSection() {
  const featuredGradients = [
    "from-purple-400 via-pink-500 to-red-500",
    "from-blue-400 via-purple-500 to-pink-500",
    "from-green-400 via-blue-500 to-purple-600",
    "from-yellow-400 via-orange-500 to-red-500",
    "from-indigo-400 via-purple-500 to-pink-500",
    "from-teal-400 via-cyan-500 to-blue-500"
  ];

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50 dark:from-purple-950/20 dark:via-pink-950/20 dark:to-orange-950/20">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      
      <div className="container mx-auto px-4 py-16 md:py-24">
        <div className="text-center space-y-8">
          {/* Badge */}
          <Badge variant="secondary" className="mx-auto">
            <Sparkles className="h-3 w-3 mr-1" />
            New gradients added daily
          </Badge>

          {/* Main Heading */}
          <div className="space-y-4">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold tracking-tight">
              Beautiful{" "}
              <span className="bg-gradient-to-r from-purple-600 via-pink-600 to-orange-600 bg-clip-text text-transparent">
                Gradient
              </span>
              <br />
              Color Palettes
            </h1>
            <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto">
              Discover thousands of hand-picked gradient color palettes for your design projects. 
              Get inspired, create your own, and share with the community.
            </p>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button size="lg" className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white">
              <Palette className="h-5 w-5 mr-2" />
              Explore Gradients
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/create">
                Create Your Own
              </Link>
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 pt-8">
            <div className="text-center">
              <div className="text-2xl md:text-3xl font-bold text-purple-600">10K+</div>
              <div className="text-sm text-muted-foreground">Gradients</div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl font-bold text-pink-600">50K+</div>
              <div className="text-sm text-muted-foreground">Downloads</div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl font-bold text-orange-600">5K+</div>
              <div className="text-sm text-muted-foreground">Designers</div>
            </div>
          </div>
        </div>

        {/* Featured Gradients Preview */}
        <div className="mt-16">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-semibold mb-2">Featured Gradients</h2>
            <p className="text-muted-foreground">Trending gradients loved by the community</p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {featuredGradients.map((gradient, index) => (
              <div
                key={index}
                className={`aspect-square rounded-xl bg-gradient-to-br ${gradient} cursor-pointer transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl`}
                onClick={() => {
                  // Handle gradient click
                }}
              />
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-6 rounded-xl bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border">
            <TrendingUp className="h-8 w-8 mx-auto mb-4 text-purple-500" />
            <h3 className="font-semibold mb-2">Trending Now</h3>
            <p className="text-sm text-muted-foreground">
              Discover the most popular gradients this week
            </p>
          </div>
          
          <div className="text-center p-6 rounded-xl bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border">
            <Palette className="h-8 w-8 mx-auto mb-4 text-pink-500" />
            <h3 className="font-semibold mb-2">Create Custom</h3>
            <p className="text-sm text-muted-foreground">
              Build your own gradients with our intuitive tools
            </p>
          </div>
          
          <div className="text-center p-6 rounded-xl bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border">
            <Users className="h-8 w-8 mx-auto mb-4 text-orange-500" />
            <h3 className="font-semibold mb-2">Join Community</h3>
            <p className="text-sm text-muted-foreground">
              Share your creations and get inspired by others
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
