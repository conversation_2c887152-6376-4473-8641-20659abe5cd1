"use client";

import { useState, useEffect } from "react";
import { useSearch } from "@/contexts/SearchContext";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Filter,
  TrendingUp,
  Clock,
  Heart,
  Shuffle,
  ChevronDown,
} from "lucide-react";

export default function FilterBar() {
  const { activeFilters, sortBy, setSortBy, toggleFilter, clearFilters } =
    useSearch();
  const [activeSort, setActiveSort] = useState(sortBy);
  const [isSticky, setIsSticky] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsSticky(scrollTop > 100); // FilterBar sticky olduğunda
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const colorCategories = [
    { name: "Warm", colors: ["Red", "Orange", "Yellow", "Pink"] },
    { name: "Cool", colors: ["Blue", "Green", "Purple", "Teal"] },
    { name: "Neutral", colors: ["Gray", "Black", "White", "Beige"] },
    {
      name: "Pastel",
      colors: ["Light Pink", "Light Blue", "Mint", "Lavender"],
    },
  ];

  const styleCategories = [
    "Gradient",
    "Linear",
    "Radial",
    "Conic",
    "Vintage",
    "Modern",
    "Neon",
    "Sunset",
    "Ocean",
    "Forest",
    "Space",
    "Retro",
  ];

  const handleSortChange = (value: string) => {
    setActiveSort(value);
    setSortBy(value);
  };

  return (
    <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-16 z-40">
      <div className={`container mx-auto px-4 ${isSticky ? "py-2" : "py-4"}`}>
        {/* Sort Tabs */}
        <div
          className={`flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 ${
            isSticky ? "mb-2" : "mb-4"
          }`}
        >
          <Tabs
            value={activeSort}
            onValueChange={handleSortChange}
            className="w-full lg:w-auto"
          >
            <TabsList className="grid w-full lg:w-auto grid-cols-4 lg:grid-cols-5">
              <TabsTrigger value="trending" className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                <span className="hidden sm:inline">Trending</span>
              </TabsTrigger>
              <TabsTrigger value="new" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <span className="hidden sm:inline">New</span>
              </TabsTrigger>
              <TabsTrigger value="popular" className="flex items-center gap-2">
                <Heart className="h-4 w-4" />
                <span className="hidden sm:inline">Popular</span>
              </TabsTrigger>
              <TabsTrigger value="random" className="flex items-center gap-2">
                <Shuffle className="h-4 w-4" />
                <span className="hidden sm:inline">Random</span>
              </TabsTrigger>
              <TabsTrigger
                value="collections"
                className="hidden lg:flex items-center gap-2"
              >
                Collections
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <Button variant="outline" size="sm" className="lg:w-auto">
            <Filter className="h-4 w-4 mr-2" />
            Filters
            <ChevronDown className="h-4 w-4 ml-2" />
          </Button>
        </div>

        {/* Color Categories - Hidden when sticky */}
        {!isSticky && (
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium mb-3">Color Categories</h3>
              <div className="flex flex-wrap gap-2">
                {colorCategories.map((category) => (
                  <Button
                    key={category.name}
                    variant={
                      activeFilters.includes(category.name)
                        ? "default"
                        : "outline"
                    }
                    size="sm"
                    onClick={() => toggleFilter(category.name)}
                    className="text-xs"
                  >
                    {category.name}
                  </Button>
                ))}
              </div>
            </div>

            {/* Style Categories */}
            <div>
              <h3 className="text-sm font-medium mb-3">Styles</h3>
              <div className="flex flex-wrap gap-2">
                {styleCategories.map((style) => (
                  <Button
                    key={style}
                    variant={
                      activeFilters.includes(style) ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() => toggleFilter(style)}
                    className="text-xs"
                  >
                    {style}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Active Filters */}
        {activeFilters.length > 0 && (
          <div
            className={`flex items-center gap-2 ${
              isSticky ? "pt-0" : "pt-2 border-t"
            }`}
          >
            {!isSticky && (
              <span className="text-sm text-muted-foreground">
                Active filters:
              </span>
            )}
            <div className="flex flex-wrap gap-1">
              {activeFilters.map((filter) => (
                <Badge
                  key={filter}
                  variant="secondary"
                  className={`cursor-pointer hover:bg-destructive hover:text-destructive-foreground ${
                    isSticky ? "text-xs" : ""
                  }`}
                  onClick={() => toggleFilter(filter)}
                >
                  {filter} ×
                </Badge>
              ))}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="text-xs"
            >
              {isSticky ? "Clear" : "Clear all"}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
