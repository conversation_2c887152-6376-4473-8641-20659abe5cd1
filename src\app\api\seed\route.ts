import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Gradient from '@/models/Gradient';
import User from '@/models/User';
import Collection from '@/models/Collection';

export async function POST() {
  try {
    await connectDB();
    
    // Clear existing data
    await Gradient.deleteMany({});
    await User.deleteMany({});
    await Collection.deleteMany({});
    
    // Create sample users
    const users = await User.insertMany([
      {
        username: 'gradientmaster',
        email: '<EMAIL>',
        password: 'hashedpassword123',
        bio: 'Professional gradient designer with 5+ years experience',
        stats: { gradientsCreated: 15, totalLikes: 234, totalViews: 1250 }
      },
      {
        username: 'colorwizard',
        email: '<EMAIL>', 
        password: 'hashedpassword123',
        bio: 'Color theory enthusiast and digital artist',
        stats: { gradientsCreated: 8, totalLikes: 156, totalViews: 890 }
      },
      {
        username: 'designp<PERSON>',
        email: '<EMAIL>',
        password: 'hashedpassword123',
        bio: 'UI/UX designer specializing in modern color schemes',
        stats: { gradientsCreated: 12, totalLikes: 189, totalViews: 1050 }
      }
    ]);

    // Create sample gradients
    const gradients = await Gradient.insertMany([
      {
        name: 'Ocean Sunset',
        description: 'Beautiful ocean sunset gradient with warm pink tones',
        colors: ['#ff9a9e', '#fecfef', '#fecfef'],
        css: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)',
        tailwind: 'bg-gradient-to-br from-pink-400 via-pink-200 to-pink-200',
        type: 'linear',
        angle: 135,
        author: users[0]._id,
        tags: ['sunset', 'ocean', 'warm', 'pink'],
        category: 'warm',
        stats: { views: 245, likes: 34, downloads: 12, copies: 8 },
        metadata: {
          colorStops: [
            { color: '#ff9a9e', position: 0 },
            { color: '#fecfef', position: 50 },
            { color: '#fecfef', position: 100 }
          ],
          blenderColors: [
            { r: 1, g: 0.6, b: 0.62, a: 1 },
            { r: 0.996, g: 0.812, b: 0.937, a: 1 },
            { r: 0.996, g: 0.812, b: 0.937, a: 1 }
          ],
          accessibility: { colorBlindSafe: false }
        }
      },
      {
        name: 'Space Nebula',
        description: 'Cosmic space nebula gradient with deep purples and blues',
        colors: ['#667eea', '#764ba2', '#f093fb'],
        css: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
        tailwind: 'bg-gradient-to-br from-blue-400 via-purple-600 to-pink-300',
        type: 'linear',
        angle: 135,
        author: users[0]._id,
        tags: ['space', 'nebula', 'cosmic', 'purple'],
        category: 'space',
        stats: { views: 189, likes: 28, downloads: 9, copies: 15 },
        metadata: {
          colorStops: [
            { color: '#667eea', position: 0 },
            { color: '#764ba2', position: 50 },
            { color: '#f093fb', position: 100 }
          ],
          blenderColors: [
            { r: 0.4, g: 0.494, b: 0.918, a: 1 },
            { r: 0.463, g: 0.294, b: 0.635, a: 1 },
            { r: 0.941, g: 0.576, b: 0.984, a: 1 }
          ],
          accessibility: { colorBlindSafe: false }
        }
      },
      {
        name: 'Mint Fresh',
        description: 'Fresh mint gradient perfect for spring designs',
        colors: ['#a8edea', '#fed6e3'],
        css: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
        tailwind: 'bg-gradient-to-br from-teal-200 to-pink-200',
        type: 'linear',
        angle: 135,
        author: users[1]._id,
        tags: ['mint', 'fresh', 'pastel', 'spring'],
        category: 'pastel',
        stats: { views: 156, likes: 22, downloads: 7, copies: 11 },
        metadata: {
          colorStops: [
            { color: '#a8edea', position: 0 },
            { color: '#fed6e3', position: 100 }
          ],
          blenderColors: [
            { r: 0.659, g: 0.929, b: 0.918, a: 1 },
            { r: 0.996, g: 0.839, b: 0.890, a: 1 }
          ],
          accessibility: { colorBlindSafe: true }
        }
      },
      {
        name: 'Neon Cyber',
        description: 'Vibrant neon gradient for cyberpunk aesthetics',
        colors: ['#ff0099', '#493240', '#00ff88'],
        css: 'linear-gradient(135deg, #ff0099 0%, #493240 50%, #00ff88 100%)',
        tailwind: 'bg-gradient-to-br from-pink-500 via-gray-800 to-green-400',
        type: 'linear',
        angle: 135,
        author: users[1]._id,
        tags: ['neon', 'cyber', 'vibrant', 'electric'],
        category: 'neon',
        stats: { views: 298, likes: 45, downloads: 18, copies: 23 },
        metadata: {
          colorStops: [
            { color: '#ff0099', position: 0 },
            { color: '#493240', position: 50 },
            { color: '#00ff88', position: 100 }
          ],
          blenderColors: [
            { r: 1, g: 0, b: 0.6, a: 1 },
            { r: 0.286, g: 0.196, b: 0.251, a: 1 },
            { r: 0, g: 1, b: 0.533, a: 1 }
          ],
          accessibility: { colorBlindSafe: false }
        }
      },
      {
        name: 'Forest Dawn',
        description: 'Natural forest gradient with morning light',
        colors: ['#11998e', '#38ef7d', '#56ab2f'],
        css: 'linear-gradient(135deg, #11998e 0%, #38ef7d 50%, #56ab2f 100%)',
        tailwind: 'bg-gradient-to-br from-teal-600 via-green-400 to-green-600',
        type: 'linear',
        angle: 135,
        author: users[2]._id,
        tags: ['forest', 'nature', 'green', 'dawn'],
        category: 'nature',
        stats: { views: 167, likes: 19, downloads: 6, copies: 9 },
        metadata: {
          colorStops: [
            { color: '#11998e', position: 0 },
            { color: '#38ef7d', position: 50 },
            { color: '#56ab2f', position: 100 }
          ],
          blenderColors: [
            { r: 0.067, g: 0.6, b: 0.557, a: 1 },
            { r: 0.220, g: 0.937, b: 0.490, a: 1 },
            { r: 0.337, g: 0.671, b: 0.184, a: 1 }
          ],
          accessibility: { colorBlindSafe: true }
        }
      },
      {
        name: 'Golden Hour',
        description: 'Warm golden hour gradient with orange and yellow tones',
        colors: ['#ff9a56', '#ffad56', '#ffd56b'],
        css: 'linear-gradient(135deg, #ff9a56 0%, #ffad56 50%, #ffd56b 100%)',
        tailwind: 'bg-gradient-to-br from-orange-400 via-orange-300 to-yellow-300',
        type: 'linear',
        angle: 135,
        author: users[2]._id,
        tags: ['golden', 'hour', 'warm', 'sunset'],
        category: 'warm',
        stats: { views: 234, likes: 31, downloads: 14, copies: 17 },
        metadata: {
          colorStops: [
            { color: '#ff9a56', position: 0 },
            { color: '#ffad56', position: 50 },
            { color: '#ffd56b', position: 100 }
          ],
          blenderColors: [
            { r: 1, g: 0.604, b: 0.337, a: 1 },
            { r: 1, g: 0.678, b: 0.337, a: 1 },
            { r: 1, g: 0.835, b: 0.420, a: 1 }
          ],
          accessibility: { colorBlindSafe: true }
        }
      }
    ]);

    // Create sample collections
    const collections = await Collection.insertMany([
      {
        name: 'Sunset Vibes',
        description: 'Warm and cozy gradients inspired by beautiful sunsets around the world',
        author: users[0]._id,
        gradients: [gradients[0]._id, gradients[5]._id],
        thumbnail: ['#ff9a9e', '#fecfef', '#ff9a56'],
        tags: ['sunset', 'warm', 'cozy'],
        stats: { views: 89, likes: 12, saves: 8 }
      },
      {
        name: 'Neon Dreams',
        description: 'Vibrant neon gradients perfect for modern digital designs',
        author: users[1]._id,
        gradients: [gradients[3]._id],
        thumbnail: ['#ff0099', '#493240', '#00ff88'],
        tags: ['neon', 'vibrant', 'modern'],
        stats: { views: 67, likes: 9, saves: 5 }
      },
      {
        name: 'Nature Collection',
        description: 'Natural gradients inspired by the beauty of nature',
        author: users[2]._id,
        gradients: [gradients[2]._id, gradients[4]._id],
        thumbnail: ['#a8edea', '#11998e', '#38ef7d'],
        tags: ['nature', 'green', 'fresh'],
        stats: { views: 45, likes: 6, saves: 3 }
      }
    ]);

    return NextResponse.json({
      message: 'Database seeded successfully!',
      data: {
        users: users.length,
        gradients: gradients.length,
        collections: collections.length
      }
    });

  } catch (error) {
    console.error('Error seeding database:', error);
    return NextResponse.json(
      { 
        error: 'Failed to seed database',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
