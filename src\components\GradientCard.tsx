"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { 
  Heart, 
  Copy, 
  Download, 
  Share2, 
  Palette,
  Check,
  Eye
} from "lucide-react";

interface GradientCardProps {
  gradient: {
    id: string;
    name: string;
    colors: string[];
    css: string;
    tailwind: string;
    likes: number;
    views: number;
    tags: string[];
    author?: string;
    isLiked?: boolean;
  };
}

export default function GradientCard({ gradient }: GradientCardProps) {
  const [isLiked, setIsLiked] = useState(gradient.isLiked || false);
  const [likes, setLikes] = useState(gradient.likes);
  const [copiedFormat, setCopiedFormat] = useState<string | null>(null);

  const handleLike = () => {
    setIsLiked(!isLiked);
    setLikes(prev => isLiked ? prev - 1 : prev + 1);
  };

  const copyToClipboard = async (text: string, format: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedFormat(format);
      setTimeout(() => setCopiedFormat(null), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const getColorHex = (color: string) => {
    // This would normally convert Tailwind colors to hex
    // For demo purposes, returning the color as is
    return color;
  };

  return (
    <TooltipProvider>
      <Card className="group overflow-hidden hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
        {/* Gradient Preview */}
        <div 
          className="aspect-[4/3] cursor-pointer relative overflow-hidden"
          style={{ background: gradient.css }}
        >
          {/* Overlay on hover */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center">
            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex gap-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="sm"
                    variant="secondary"
                    className="bg-white/90 hover:bg-white text-black"
                    onClick={() => copyToClipboard(gradient.css, 'css')}
                  >
                    {copiedFormat === 'css' ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{copiedFormat === 'css' ? 'Copied!' : 'Copy CSS'}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="sm"
                    variant="secondary"
                    className="bg-white/90 hover:bg-white text-black"
                    onClick={() => copyToClipboard(gradient.tailwind, 'tailwind')}
                  >
                    {copiedFormat === 'tailwind' ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <Palette className="h-4 w-4" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{copiedFormat === 'tailwind' ? 'Copied!' : 'Copy Tailwind'}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="sm"
                    variant="secondary"
                    className="bg-white/90 hover:bg-white text-black"
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Share</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </div>

          {/* Color dots indicator */}
          <div className="absolute top-3 left-3 flex gap-1">
            {gradient.colors.map((color, index) => (
              <div
                key={index}
                className="w-3 h-3 rounded-full border-2 border-white/80 shadow-sm"
                style={{ backgroundColor: color }}
              />
            ))}
          </div>
        </div>

        {/* Card Content */}
        <div className="p-4">
          {/* Header */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-sm truncate">{gradient.name}</h3>
              {gradient.author && (
                <p className="text-xs text-muted-foreground">by {gradient.author}</p>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLike}
              className={`ml-2 ${isLiked ? 'text-red-500' : 'text-muted-foreground'}`}
            >
              <Heart className={`h-4 w-4 ${isLiked ? 'fill-current' : ''}`} />
            </Button>
          </div>

          {/* Tags */}
          {gradient.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-3">
              {gradient.tags.slice(0, 3).map((tag) => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {gradient.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{gradient.tags.length - 3}
                </Badge>
              )}
            </div>
          )}

          {/* Color Values */}
          <div className="space-y-2 mb-3">
            <div className="flex flex-wrap gap-1">
              {gradient.colors.map((color, index) => (
                <Tooltip key={index}>
                  <TooltipTrigger asChild>
                    <button
                      className="text-xs font-mono bg-muted px-2 py-1 rounded hover:bg-muted/80 transition-colors"
                      onClick={() => copyToClipboard(color, `color-${index}`)}
                    >
                      {copiedFormat === `color-${index}` ? (
                        <Check className="h-3 w-3 inline" />
                      ) : (
                        getColorHex(color)
                      )}
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{copiedFormat === `color-${index}` ? 'Copied!' : 'Click to copy'}</p>
                  </TooltipContent>
                </Tooltip>
              ))}
            </div>
          </div>

          {/* Stats */}
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-3">
              <span className="flex items-center gap-1">
                <Heart className="h-3 w-3" />
                {likes}
              </span>
              <span className="flex items-center gap-1">
                <Eye className="h-3 w-3" />
                {gradient.views}
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="h-auto p-0 text-xs"
            >
              <Download className="h-3 w-3 mr-1" />
              Export
            </Button>
          </div>
        </div>
      </Card>
    </TooltipProvider>
  );
}
