"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import GradientCard from "./GradientCard";
import { TrendingUp, Clock, Calendar, Award, Eye, Heart } from "lucide-react";

// Sample trending data
const trendingGradients = [
  {
    id: "t1",
    name: "Cosmic Burst",
    colors: ["#667eea", "#764ba2", "#f093fb"],
    css: "linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)",
    tailwind: "bg-gradient-to-br from-blue-400 via-purple-600 to-pink-300",
    likes: 4567,
    views: 12890,
    tags: ["space", "cosmic", "vibrant"],
    author: "<PERSON>Designer",
    isLiked: false,
    trend: "+234%",
    period: "This week",
  },
  {
    id: "t2",
    name: "Ocean Sunset",
    colors: ["#ff9a9e", "#fecfef", "#fecfef"],
    css: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)",
    tailwind: "bg-gradient-to-br from-pink-400 via-pink-200 to-pink-200",
    likes: 3456,
    views: 9876,
    tags: ["sunset", "ocean", "warm"],
    author: "OceanVibes",
    isLiked: true,
    trend: "+189%",
    period: "This week",
  },
  // Add more trending gradients...
];

const topCreators = [
  { name: "SpaceDesigner", gradients: 45, likes: 12890, avatar: "🚀" },
  { name: "OceanVibes", gradients: 32, likes: 9876, avatar: "🌊" },
  { name: "ColorMaster", gradients: 28, likes: 8765, avatar: "🎨" },
  { name: "DesignGuru", gradients: 24, likes: 7654, avatar: "✨" },
];

export default function TrendingContent() {
  const [timeframe, setTimeframe] = useState("week");
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/stats?period=${timeframe}`);
        const data = await response.json();
        setStats(data);
      } catch (error) {
        console.error("Error fetching stats:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [timeframe]);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl md:text-4xl font-bold mb-4">
          <TrendingUp className="inline h-8 w-8 mr-2 text-orange-500" />
          Trending Gradients
        </h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Discover the hottest gradient color palettes that are taking the
          design world by storm. See what's popular and get inspired by trending
          designs.
        </p>
      </div>

      {/* Time Filter */}
      <div className="flex justify-center mb-8">
        <Tabs value={timeframe} onValueChange={setTimeframe}>
          <TabsList>
            <TabsTrigger value="day">Today</TabsTrigger>
            <TabsTrigger value="week">This Week</TabsTrigger>
            <TabsTrigger value="month">This Month</TabsTrigger>
            <TabsTrigger value="year">This Year</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-3">
          {/* Top Trending */}
          <div className="mb-8">
            <h2 className="text-2xl font-semibold mb-4 flex items-center">
              <Award className="h-6 w-6 mr-2 text-yellow-500" />
              Top Trending
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {loading
                ? Array.from({ length: 6 }).map((_, index) => (
                    <div key={index} className="animate-pulse">
                      <div className="h-48 bg-muted rounded-lg"></div>
                    </div>
                  ))
                : stats?.trendingGradients
                    ?.slice(0, 6)
                    .map((gradient: any, index: number) => (
                      <div key={gradient._id} className="relative">
                        <Badge className="absolute -top-2 -left-2 z-10 bg-orange-500 text-white">
                          #{index + 1}
                        </Badge>
                        <GradientCard
                          gradient={{
                            id: gradient._id,
                            name: gradient.name,
                            colors: gradient.colors,
                            css: `linear-gradient(135deg, ${gradient.colors.join(
                              ", "
                            )})`,
                            tailwind: `bg-gradient-to-br from-${
                              gradient.colors[0]
                            } to-${
                              gradient.colors[gradient.colors.length - 1]
                            }`,
                            likes: gradient.stats.likes,
                            views: gradient.stats.views,
                            tags: [],
                            author: gradient.author.username,
                            isLiked: false,
                          }}
                        />
                        <div className="mt-2 text-center">
                          <Badge
                            variant="outline"
                            className="text-green-600 border-green-600"
                          >
                            {gradient.stats.views} views
                          </Badge>
                        </div>
                      </div>
                    ))}
            </div>
          </div>

          {/* Rising Stars */}
          <div className="mb-8">
            <h2 className="text-2xl font-semibold mb-4 flex items-center">
              <TrendingUp className="h-6 w-6 mr-2 text-green-500" />
              Rising Stars
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {trendingGradients.slice(6, 12).map((gradient) => (
                <GradientCard key={gradient.id} gradient={gradient} />
              ))}
            </div>
          </div>

          {/* Recently Popular */}
          <div>
            <h2 className="text-2xl font-semibold mb-4 flex items-center">
              <Clock className="h-6 w-6 mr-2 text-blue-500" />
              Recently Popular
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {trendingGradients.slice(0, 9).map((gradient) => (
                <GradientCard key={gradient.id} gradient={gradient} />
              ))}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Trending Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Trending Stats
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {loading ? (
                Array.from({ length: 4 }).map((_, index) => (
                  <div
                    key={index}
                    className="flex justify-between items-center"
                  >
                    <div className="h-4 bg-muted rounded w-20 animate-pulse"></div>
                    <div className="h-4 bg-muted rounded w-12 animate-pulse"></div>
                  </div>
                ))
              ) : (
                <>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Total Views
                    </span>
                    <span className="font-semibold">
                      {stats?.overview?.totalViews?.toLocaleString() || "0"}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Total Likes
                    </span>
                    <span className="font-semibold">
                      {stats?.overview?.totalLikes?.toLocaleString() || "0"}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      New Gradients
                    </span>
                    <span className="font-semibold">
                      +{stats?.period?.newGradients || 0}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Growth
                    </span>
                    <Badge className="bg-green-500">
                      +{stats?.growth?.gradientsGrowth || 0}%
                    </Badge>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Top Creators */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Award className="h-5 w-5 mr-2" />
                Top Creators
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {loading
                ? Array.from({ length: 4 }).map((_, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <div className="w-8 h-8 rounded-full bg-muted animate-pulse"></div>
                      <div className="flex-1 min-w-0 space-y-1">
                        <div className="h-4 bg-muted rounded w-20 animate-pulse"></div>
                        <div className="h-3 bg-muted rounded w-32 animate-pulse"></div>
                      </div>
                      <div className="h-6 bg-muted rounded w-8 animate-pulse"></div>
                    </div>
                  ))
                : stats?.topCreators?.map((creator: any, index: number) => (
                    <div
                      key={creator._id}
                      className="flex items-center space-x-3"
                    >
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-muted">
                        <span className="text-lg">👤</span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {creator.username}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {creator.stats.gradientsCreated} gradients •{" "}
                          {creator.stats.totalLikes} likes
                        </p>
                      </div>
                      <Badge variant="outline">#{index + 1}</Badge>
                    </div>
                  ))}
            </CardContent>
          </Card>

          {/* Trending Categories */}
          <Card>
            <CardHeader>
              <CardTitle>Trending Categories</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {["Space", "Ocean", "Sunset", "Neon", "Pastel", "Vintage"].map(
                (category) => (
                  <Button
                    key={category}
                    variant="ghost"
                    className="w-full justify-start"
                  >
                    {category}
                  </Button>
                )
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
