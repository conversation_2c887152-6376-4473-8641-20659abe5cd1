import { Suspense } from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import HeroSection from "@/components/HeroSection";
import FilterBar from "@/components/FilterBar";
import GradientGrid from "@/components/GradientGrid";
import Loading from "@/components/Loading";

export default function Home() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        <HeroSection />
        <FilterBar />
        <Suspense
          fallback={
            <Loading size="lg" text="Loading gradients..." className="py-12" />
          }
        >
          <GradientGrid />
        </Suspense>
      </main>
      <Footer />
    </div>
  );
}
