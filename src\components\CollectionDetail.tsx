"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import GradientCard from "./GradientCard";
import Loading from "./Loading";
import { 
  Heart, 
  Eye, 
  Share2, 
  Download,
  ArrowLeft,
  User,
  Calendar,
  Bookmark
} from "lucide-react";
import Link from "next/link";

interface CollectionDetailProps {
  collectionId: string;
}

interface Collection {
  _id: string;
  name: string;
  description: string;
  author: {
    _id: string;
    username: string;
    avatar?: string;
  };
  gradients: any[];
  thumbnail: string[];
  tags: string[];
  stats: {
    views: number;
    likes: number;
    saves: number;
  };
  createdAt: string;
}

export default function CollectionDetail({ collectionId }: CollectionDetailProps) {
  const [collection, setCollection] = useState<Collection | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLiked, setIsLiked] = useState(false);
  const [isSaved, setIsSaved] = useState(false);

  useEffect(() => {
    const fetchCollection = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/collections/${collectionId}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch collection');
        }

        const data = await response.json();
        setCollection(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    if (collectionId) {
      fetchCollection();
    }
  }, [collectionId]);

  const handleLike = () => {
    setIsLiked(!isLiked);
    // TODO: API call to like/unlike collection
  };

  const handleSave = () => {
    setIsSaved(!isSaved);
    // TODO: API call to save/unsave collection
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: collection?.name,
        text: collection?.description,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Loading size="lg" text="Loading collection..." className="py-12" />
      </div>
    );
  }

  if (error || !collection) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <div className="text-6xl mb-4">😞</div>
          <h3 className="text-xl font-semibold mb-2">Collection not found</h3>
          <p className="text-muted-foreground mb-4">
            {error || 'The collection you are looking for does not exist.'}
          </p>
          <Link href="/collections">
            <Button>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Collections
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back Button */}
      <div className="mb-6">
        <Link href="/collections">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Collections
          </Button>
        </Link>
      </div>

      {/* Collection Header */}
      <div className="mb-8">
        <Card>
          <CardHeader>
            {/* Thumbnail */}
            <div className="flex gap-1 mb-4 h-24 rounded-lg overflow-hidden">
              {collection.thumbnail.map((color, index) => (
                <div
                  key={index}
                  className="flex-1"
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>

            <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-4">
              <div className="flex-1">
                <CardTitle className="text-2xl md:text-3xl mb-2">
                  {collection.name}
                </CardTitle>
                <p className="text-muted-foreground mb-4">
                  {collection.description}
                </p>

                {/* Author and Meta */}
                <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-4">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span>by {collection.author.username}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span>{new Date(collection.createdAt).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    <span>{collection.stats.views} views</span>
                  </div>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {collection.tags.map((tag) => (
                    <Badge key={tag} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                <Button
                  variant={isLiked ? "default" : "outline"}
                  size="sm"
                  onClick={handleLike}
                >
                  <Heart className={`h-4 w-4 mr-2 ${isLiked ? 'fill-current' : ''}`} />
                  {collection.stats.likes + (isLiked ? 1 : 0)}
                </Button>
                <Button
                  variant={isSaved ? "default" : "outline"}
                  size="sm"
                  onClick={handleSave}
                >
                  <Bookmark className={`h-4 w-4 mr-2 ${isSaved ? 'fill-current' : ''}`} />
                  Save
                </Button>
                <Button variant="outline" size="sm" onClick={handleShare}>
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export All
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>
      </div>

      {/* Collection Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">
              {collection.gradients.length}
            </div>
            <div className="text-sm text-muted-foreground">Gradients</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">
              {collection.stats.likes}
            </div>
            <div className="text-sm text-muted-foreground">Likes</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">
              {collection.stats.saves}
            </div>
            <div className="text-sm text-muted-foreground">Saves</div>
          </CardContent>
        </Card>
      </div>

      {/* Gradients */}
      <div>
        <h2 className="text-xl font-semibold mb-6">
          Gradients in this collection ({collection.gradients.length})
        </h2>
        
        {collection.gradients.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🎨</div>
            <h3 className="text-xl font-semibold mb-2">No gradients yet</h3>
            <p className="text-muted-foreground">
              This collection doesn't have any gradients yet.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {collection.gradients.map((gradient) => (
              <GradientCard
                key={gradient._id}
                gradient={{
                  id: gradient._id,
                  name: gradient.name,
                  colors: gradient.colors,
                  css: gradient.css,
                  tailwind: gradient.tailwind,
                  likes: gradient.stats?.likes || 0,
                  views: gradient.stats?.views || 0,
                  tags: gradient.tags || [],
                  author: gradient.author?.username || 'Unknown',
                  isLiked: false
                }}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
