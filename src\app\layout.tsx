import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { SearchProvider } from "@/contexts/SearchContext";
import ErrorBoundary from "@/components/ErrorBoundary";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    default: "Gradient Hunt - Beautiful Gradient Color Palettes",
    template: "%s | Gradient Hunt",
  },
  description:
    "Discover and create beautiful gradient color palettes for your design projects. Get inspired by hand-picked gradients and create your own.",
  keywords: [
    "gradient",
    "color palette",
    "design",
    "css gradient",
    "tailwind gradient",
    "color scheme",
    "web design",
    "ui design",
    "blender colors",
  ],
  authors: [{ name: "<PERSON><PERSON><PERSON> Hunt" }],
  creator: "<PERSON><PERSON><PERSON> Hunt",
  publisher: "<PERSON><PERSON><PERSON> Hunt",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://gradient-hunt.com"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://gradient-hunt.com",
    title: "Gradient Hunt - Beautiful Gradient Color Palettes",
    description:
      "Discover and create beautiful gradient color palettes for your design projects.",
    siteName: "Gradient Hunt",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "Gradient Hunt - Beautiful Gradient Color Palettes",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Gradient Hunt - Beautiful Gradient Color Palettes",
    description:
      "Discover and create beautiful gradient color palettes for your design projects.",
    images: ["/og-image.png"],
    creator: "@gradienthunt",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ErrorBoundary>
          <SearchProvider>{children}</SearchProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
