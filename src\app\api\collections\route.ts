import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Collection from '@/models/Collection';
import User from '@/models/User';

// GET /api/collections - Fetch collections with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const search = searchParams.get('search');
    const sortBy = searchParams.get('sortBy') || 'popular';
    const author = searchParams.get('author');

    // Build query
    const query: any = { isPublic: true };

    if (author) {
      query.author = author;
    }

    if (search) {
      query.$text = { $search: search };
    }

    // Build sort
    let sort: any = {};
    switch (sortBy) {
      case 'new':
        sort = { createdAt: -1 };
        break;
      case 'likes':
        sort = { 'stats.likes': -1 };
        break;
      case 'saves':
        sort = { 'stats.saves': -1 };
        break;
      default: // popular
        sort = { 'stats.likes': -1, 'stats.saves': -1 };
    }

    const skip = (page - 1) * limit;

    const [collections, total] = await Promise.all([
      Collection.find(query)
        .populate('author', 'username avatar')
        .populate('gradients', 'name colors css')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      Collection.countDocuments(query)
    ]);

    return NextResponse.json({
      collections,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching collections:', error);
    return NextResponse.json(
      { error: 'Failed to fetch collections' },
      { status: 500 }
    );
  }
}

// POST /api/collections - Create new collection
export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const {
      name,
      description,
      author,
      gradients,
      tags,
      isPublic
    } = body;

    // Validate required fields
    if (!name || !description || !author) {
      return NextResponse.json(
        { error: 'Name, description, and author are required' },
        { status: 400 }
      );
    }

    // Validate author exists
    const user = await User.findById(author);
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid author' },
        { status: 400 }
      );
    }

    // Generate thumbnail from first few gradients
    let thumbnail: string[] = [];
    if (gradients && gradients.length > 0) {
      const gradientDocs = await Gradient.find({
        _id: { $in: gradients.slice(0, 3) }
      }).select('colors');
      
      thumbnail = gradientDocs.flatMap(g => g.colors.slice(0, 2)).slice(0, 6);
    }

    // Create collection
    const collection = new Collection({
      name,
      description,
      author,
      gradients: gradients || [],
      thumbnail,
      tags: tags || [],
      isPublic: isPublic !== false // Default to true
    });

    await collection.save();

    // Populate author details
    await collection.populate('author', 'username avatar');

    return NextResponse.json(collection, { status: 201 });

  } catch (error) {
    console.error('Error creating collection:', error);
    return NextResponse.json(
      { error: 'Failed to create collection' },
      { status: 500 }
    );
  }
}
