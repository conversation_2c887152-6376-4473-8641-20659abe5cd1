"use client";

import { useState, useEffect, useMemo } from "react";
import GradientCard from "./GradientCard";
import { useSearch } from "@/contexts/SearchContext";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

// Sample gradient data - in a real app, this would come from an API
const sampleGradients = [
  {
    id: "1",
    name: "Sunset Vibes",
    colors: ["#FF6B6B", "#4ECDC4", "#45B7D1"],
    css: "linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%)",
    tailwind: "bg-gradient-to-br from-red-400 via-teal-400 to-blue-400",
    likes: 1234,
    views: 5678,
    tags: ["warm", "sunset", "vibrant"],
    author: "DesignMaster",
    isLiked: false,
  },
  {
    id: "2",
    name: "Ocean Depths",
    colors: ["#667eea", "#764ba2"],
    css: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
    tailwind: "bg-gradient-to-br from-blue-400 to-purple-600",
    likes: 987,
    views: 3456,
    tags: ["cool", "ocean", "deep"],
    author: "WaveDesigner",
    isLiked: true,
  },
  {
    id: "3",
    name: "Forest Dream",
    colors: ["#11998e", "#38ef7d"],
    css: "linear-gradient(135deg, #11998e 0%, #38ef7d 100%)",
    tailwind: "bg-gradient-to-br from-teal-600 to-green-400",
    likes: 756,
    views: 2341,
    tags: ["nature", "green", "fresh"],
    author: "NatureLover",
    isLiked: false,
  },
  {
    id: "4",
    name: "Purple Rain",
    colors: ["#8360c3", "#2ebf91"],
    css: "linear-gradient(135deg, #8360c3 0%, #2ebf91 100%)",
    tailwind: "bg-gradient-to-br from-purple-500 to-teal-500",
    likes: 1456,
    views: 6789,
    tags: ["purple", "rain", "mystical"],
    author: "ColorWizard",
    isLiked: false,
  },
  {
    id: "5",
    name: "Fire & Ice",
    colors: ["#ff9a9e", "#fecfef", "#fecfef"],
    css: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)",
    tailwind: "bg-gradient-to-br from-pink-400 via-pink-200 to-pink-200",
    likes: 2134,
    views: 8765,
    tags: ["pink", "soft", "pastel"],
    author: "SoftDesigns",
    isLiked: true,
  },
  {
    id: "6",
    name: "Golden Hour",
    colors: ["#f093fb", "#f5576c"],
    css: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
    tailwind: "bg-gradient-to-br from-pink-300 to-red-400",
    likes: 1876,
    views: 4532,
    tags: ["golden", "warm", "romantic"],
    author: "GoldenTouch",
    isLiked: false,
  },
  {
    id: "7",
    name: "Midnight Blue",
    colors: ["#2c3e50", "#3498db"],
    css: "linear-gradient(135deg, #2c3e50 0%, #3498db 100%)",
    tailwind: "bg-gradient-to-br from-slate-700 to-blue-500",
    likes: 1098,
    views: 3210,
    tags: ["dark", "blue", "night"],
    author: "MidnightCoder",
    isLiked: false,
  },
  {
    id: "8",
    name: "Tropical Paradise",
    colors: ["#00d2ff", "#3a7bd5"],
    css: "linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%)",
    tailwind: "bg-gradient-to-br from-cyan-400 to-blue-600",
    likes: 2567,
    views: 7890,
    tags: ["tropical", "blue", "paradise"],
    author: "TropicalVibes",
    isLiked: true,
  },
  {
    id: "9",
    name: "Autumn Leaves",
    colors: ["#f7971e", "#ffd200"],
    css: "linear-gradient(135deg, #f7971e 0%, #ffd200 100%)",
    tailwind: "bg-gradient-to-br from-orange-500 to-yellow-400",
    likes: 1345,
    views: 4567,
    tags: ["autumn", "orange", "warm"],
    author: "AutumnArt",
    isLiked: false,
  },
  {
    id: "10",
    name: "Space Nebula",
    colors: ["#667eea", "#764ba2", "#f093fb"],
    css: "linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)",
    tailwind: "bg-gradient-to-br from-blue-400 via-purple-600 to-pink-300",
    likes: 3456,
    views: 9876,
    tags: ["space", "nebula", "cosmic"],
    author: "SpaceExplorer",
    isLiked: false,
  },
  {
    id: "11",
    name: "Mint Fresh",
    colors: ["#a8edea", "#fed6e3"],
    css: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
    tailwind: "bg-gradient-to-br from-teal-200 to-pink-200",
    likes: 987,
    views: 2345,
    tags: ["mint", "fresh", "pastel"],
    author: "FreshMint",
    isLiked: true,
  },
  {
    id: "12",
    name: "Electric Dreams",
    colors: ["#ff0099", "#493240"],
    css: "linear-gradient(135deg, #ff0099 0%, #493240 100%)",
    tailwind: "bg-gradient-to-br from-pink-500 to-gray-800",
    likes: 2109,
    views: 5432,
    tags: ["electric", "neon", "dark"],
    author: "ElectricArt",
    isLiked: false,
  },
];

export default function GradientGrid() {
  const { searchQuery, activeFilters, sortBy } = useSearch();
  const [gradients, setGradients] = useState(sampleGradients.slice(0, 8));
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  // Filter and search gradients
  const filteredGradients = useMemo(() => {
    let filtered = [...sampleGradients];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(
        (gradient) =>
          gradient.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          gradient.tags.some((tag) =>
            tag.toLowerCase().includes(searchQuery.toLowerCase())
          ) ||
          gradient.author?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply category filters
    if (activeFilters.length > 0) {
      filtered = filtered.filter((gradient) =>
        gradient.tags.some((tag) =>
          activeFilters.some((filter) =>
            tag.toLowerCase().includes(filter.toLowerCase())
          )
        )
      );
    }

    // Apply sorting
    switch (sortBy) {
      case "popular":
        filtered.sort((a, b) => b.likes - a.likes);
        break;
      case "new":
        filtered.sort((a, b) => parseInt(b.id) - parseInt(a.id));
        break;
      case "random":
        filtered.sort(() => Math.random() - 0.5);
        break;
      default: // trending
        filtered.sort((a, b) => b.views - a.views);
    }

    return filtered;
  }, [searchQuery, activeFilters, sortBy]);

  const loadMore = async () => {
    setLoading(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const currentLength = gradients.length;
    const newGradients = sampleGradients.slice(
      currentLength,
      currentLength + 4
    );

    if (newGradients.length === 0) {
      setHasMore(false);
    } else {
      setGradients((prev) => [...prev, ...newGradients]);
    }

    setLoading(false);
  };

  return (
    <section className="container mx-auto px-4 py-8">
      {/* Results Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-semibold">
            {sortBy === "popular"
              ? "Popular"
              : sortBy === "new"
              ? "New"
              : sortBy === "random"
              ? "Random"
              : "Trending"}{" "}
            Gradients
          </h2>
          <p className="text-muted-foreground">
            {filteredGradients.length} gradients found
            {searchQuery && ` for "${searchQuery}"`}
            {activeFilters.length > 0 &&
              ` with filters: ${activeFilters.join(", ")}`}
          </p>
        </div>
      </div>

      {/* Gradient Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        {filteredGradients.slice(0, gradients.length).map((gradient) => (
          <GradientCard key={gradient.id} gradient={gradient} />
        ))}
      </div>

      {/* No Results */}
      {filteredGradients.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🎨</div>
          <h3 className="text-xl font-semibold mb-2">No gradients found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your search or filters to find what you're looking
            for.
          </p>
          <Button variant="outline" onClick={() => window.location.reload()}>
            Reset Filters
          </Button>
        </div>
      )}

      {/* Load More */}
      {hasMore && (
        <div className="text-center">
          <Button
            onClick={loadMore}
            disabled={loading}
            size="lg"
            variant="outline"
            className="min-w-[200px]"
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Loading...
              </>
            ) : (
              "Load More Gradients"
            )}
          </Button>
        </div>
      )}

      {!hasMore && (
        <div className="text-center py-8">
          <p className="text-muted-foreground">
            You've seen all the gradients! 🎨
          </p>
          <Button variant="outline" className="mt-4">
            Create Your Own
          </Button>
        </div>
      )}
    </section>
  );
}
