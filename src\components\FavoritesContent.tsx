"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import GradientCard from "./GradientCard";
import { 
  Heart, 
  Folder, 
  Grid3X3, 
  List, 
  Trash2, 
  Share2,
  Download,
  Plus
} from "lucide-react";

// Sample favorite gradients
const favoriteGradients = [
  {
    id: "f1",
    name: "Ocean Sunset",
    colors: ["#ff9a9e", "#fecfef", "#fecfef"],
    css: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)",
    tailwind: "bg-gradient-to-br from-pink-400 via-pink-200 to-pink-200",
    likes: 3456,
    views: 9876,
    tags: ["sunset", "ocean", "warm"],
    author: "OceanVibes",
    isLiked: true,
    savedAt: "2024-01-20"
  },
  {
    id: "f2",
    name: "Space Nebula",
    colors: ["#667eea", "#764ba2", "#f093fb"],
    css: "linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)",
    tailwind: "bg-gradient-to-br from-blue-400 via-purple-600 to-pink-300",
    likes: 3456,
    views: 9876,
    tags: ["space", "nebula", "cosmic"],
    author: "SpaceExplorer",
    isLiked: true,
    savedAt: "2024-01-18"
  },
  {
    id: "f3",
    name: "Mint Fresh",
    colors: ["#a8edea", "#fed6e3"],
    css: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
    tailwind: "bg-gradient-to-br from-teal-200 to-pink-200",
    likes: 987,
    views: 2345,
    tags: ["mint", "fresh", "pastel"],
    author: "FreshMint",
    isLiked: true,
    savedAt: "2024-01-15"
  }
];

const favoriteCollections = [
  {
    id: "fc1",
    name: "Sunset Vibes",
    description: "Warm and cozy gradients inspired by beautiful sunsets",
    gradientCount: 12,
    thumbnail: ["#ff9a9e", "#fecfef", "#ffd89b"],
    savedAt: "2024-01-20"
  },
  {
    id: "fc2", 
    name: "Ocean Depths",
    description: "Deep blue gradients that capture the mystery of the ocean",
    gradientCount: 8,
    thumbnail: ["#667eea", "#764ba2", "#00d2ff"],
    savedAt: "2024-01-18"
  }
];

export default function FavoritesContent() {
  const [activeTab, setActiveTab] = useState("gradients");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  const toggleSelection = (id: string) => {
    setSelectedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const clearSelection = () => setSelectedItems([]);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl md:text-4xl font-bold mb-4">
          <Heart className="inline h-8 w-8 mr-2 text-red-500" />
          My Favorites
        </h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Your saved gradient color palettes and collections. Organize, manage, and share your favorite designs.
        </p>
      </div>

      {/* Controls */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="gradients" className="flex items-center gap-2">
              <Grid3X3 className="h-4 w-4" />
              Gradients ({favoriteGradients.length})
            </TabsTrigger>
            <TabsTrigger value="collections" className="flex items-center gap-2">
              <Folder className="h-4 w-4" />
              Collections ({favoriteCollections.length})
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="flex items-center gap-2">
          {selectedItems.length > 0 && (
            <>
              <Badge variant="secondary">{selectedItems.length} selected</Badge>
              <Button variant="outline" size="sm" onClick={clearSelection}>
                Clear
              </Button>
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button variant="destructive" size="sm">
                <Trash2 className="h-4 w-4 mr-2" />
                Remove
              </Button>
            </>
          )}
          
          <div className="flex gap-1">
            <Button
              variant={viewMode === "grid" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("grid")}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsContent value="gradients">
          {favoriteGradients.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">💝</div>
              <h3 className="text-xl font-semibold mb-2">No favorite gradients yet</h3>
              <p className="text-muted-foreground mb-4">
                Start exploring and save gradients you love to see them here.
              </p>
              <Button>
                Explore Gradients
              </Button>
            </div>
          ) : (
            <>
              {viewMode === "grid" ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {favoriteGradients.map((gradient) => (
                    <div key={gradient.id} className="relative">
                      <div 
                        className={`cursor-pointer transition-all ${
                          selectedItems.includes(gradient.id) 
                            ? 'ring-2 ring-primary ring-offset-2' 
                            : ''
                        }`}
                        onClick={() => toggleSelection(gradient.id)}
                      >
                        <GradientCard gradient={gradient} />
                      </div>
                      <div className="mt-2 text-xs text-muted-foreground text-center">
                        Saved on {new Date(gradient.savedAt).toLocaleDateString()}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {favoriteGradients.map((gradient) => (
                    <Card 
                      key={gradient.id} 
                      className={`cursor-pointer transition-all ${
                        selectedItems.includes(gradient.id) 
                          ? 'ring-2 ring-primary ring-offset-2' 
                          : ''
                      }`}
                      onClick={() => toggleSelection(gradient.id)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center gap-4">
                          <div 
                            className="w-16 h-16 rounded-lg"
                            style={{ background: gradient.css }}
                          />
                          <div className="flex-1 min-w-0">
                            <h3 className="font-semibold">{gradient.name}</h3>
                            <p className="text-sm text-muted-foreground">by {gradient.author}</p>
                            <div className="flex flex-wrap gap-1 mt-2">
                              {gradient.tags.slice(0, 3).map((tag) => (
                                <Badge key={tag} variant="outline" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm text-muted-foreground">
                              Saved {new Date(gradient.savedAt).toLocaleDateString()}
                            </div>
                            <div className="flex items-center gap-3 mt-1 text-sm text-muted-foreground">
                              <span className="flex items-center gap-1">
                                <Heart className="h-3 w-3" />
                                {gradient.likes}
                              </span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </>
          )}
        </TabsContent>

        <TabsContent value="collections">
          {favoriteCollections.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📁</div>
              <h3 className="text-xl font-semibold mb-2">No favorite collections yet</h3>
              <p className="text-muted-foreground mb-4">
                Discover and save collections to organize your favorite gradients.
              </p>
              <Button>
                Browse Collections
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {favoriteCollections.map((collection) => (
                <Card 
                  key={collection.id}
                  className={`cursor-pointer transition-all ${
                    selectedItems.includes(collection.id) 
                      ? 'ring-2 ring-primary ring-offset-2' 
                      : ''
                  }`}
                  onClick={() => toggleSelection(collection.id)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex gap-1 mb-3">
                      {collection.thumbnail.map((color, index) => (
                        <div
                          key={index}
                          className="flex-1 h-16 rounded-lg"
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                    <CardTitle className="text-lg">{collection.name}</CardTitle>
                    <p className="text-sm text-muted-foreground">{collection.description}</p>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex justify-between items-center">
                      <Badge variant="secondary">{collection.gradientCount} gradients</Badge>
                      <span className="text-xs text-muted-foreground">
                        Saved {new Date(collection.savedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
