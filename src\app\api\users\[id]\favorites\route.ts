import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import Gradient from '@/models/Gradient';
import Collection from '@/models/Collection';

// GET /api/users/[id]/favorites - Get user's favorite gradients and collections
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'gradients' | 'collections' | null (both)
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');

    // Find user
    const user = await User.findById(id);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const skip = (page - 1) * limit;
    const result: any = {};

    if (!type || type === 'gradients') {
      const favoriteGradients = await Gradient.find({
        _id: { $in: user.favoriteGradients },
        isPublic: true
      })
      .populate('author', 'username avatar')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

      result.gradients = favoriteGradients;
      result.gradientCount = user.favoriteGradients.length;
    }

    if (!type || type === 'collections') {
      const favoriteCollections = await Collection.find({
        _id: { $in: user.favoriteCollections },
        isPublic: true
      })
      .populate('author', 'username avatar')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

      result.collections = favoriteCollections;
      result.collectionCount = user.favoriteCollections.length;
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error('Error fetching favorites:', error);
    return NextResponse.json(
      { error: 'Failed to fetch favorites' },
      { status: 500 }
    );
  }
}
