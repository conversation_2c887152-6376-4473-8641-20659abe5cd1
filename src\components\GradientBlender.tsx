"use client";

import { useState, useRef } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import {
  Download,
  Plus,
  Minus,
  Palette,
  <PERSON>otateCc<PERSON>,
  <PERSON><PERSON>,
  Save,
} from "lucide-react";

interface ColorStop {
  id: string;
  color: string;
  position: number;
}

export default function GradientBlender() {
  const [colorStops, setColorStops] = useState<ColorStop[]>([
    { id: "1", color: "#FFD700", position: 0 },
    { id: "2", color: "#FF69B4", position: 25 },
    { id: "3", color: "#FF6347", position: 50 },
    { id: "4", color: "#1E90FF", position: 75 },
    { id: "5", color: "#4169E1", position: 100 },
  ]);

  const [direction, setDirection] = useState(90);
  const [gradientName, setGradientName] = useState("Vector Gradient Color Set");
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const generateGradientCSS = () => {
    const sortedStops = [...colorStops].sort((a, b) => a.position - b.position);
    const colorString = sortedStops
      .map((stop) => `${stop.color} ${stop.position}%`)
      .join(", ");
    return `linear-gradient(${direction}deg, ${colorString})`;
  };

  const addColorStop = () => {
    const newId = Date.now().toString();
    const newPosition =
      colorStops.length > 0
        ? Math.max(...colorStops.map((s) => s.position)) + 10
        : 0;

    setColorStops([
      ...colorStops,
      {
        id: newId,
        color: "#000000",
        position: Math.min(newPosition, 100),
      },
    ]);
  };

  const removeColorStop = (id: string) => {
    if (colorStops.length > 2) {
      setColorStops(colorStops.filter((stop) => stop.id !== id));
    }
  };

  const updateColorStop = (id: string, updates: Partial<ColorStop>) => {
    setColorStops(
      colorStops.map((stop) =>
        stop.id === id ? { ...stop, ...updates } : stop
      )
    );
  };

  const resetGradient = () => {
    setColorStops([
      { id: "1", color: "#FFD700", position: 0 },
      { id: "2", color: "#FF69B4", position: 25 },
      { id: "3", color: "#FF6347", position: 50 },
      { id: "4", color: "#1E90FF", position: 75 },
      { id: "5", color: "#4169E1", position: 100 },
    ]);
    setDirection(90);
  };

  const copyCSS = () => {
    const css = generateGradientCSS();
    navigator.clipboard.writeText(css);
    alert("CSS copied to clipboard!");
  };

  const drawRoundedRect = (
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    width: number,
    height: number,
    radius: number
  ) => {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
  };

  const downloadPNG = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Set canvas size for high quality
    canvas.width = 1200;
    canvas.height = 800;

    // Background
    ctx.fillStyle = "#f8f9fa";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Add title
    ctx.fillStyle = "#666";
    ctx.font = "bold 32px Arial";
    ctx.textAlign = "center";
    ctx.fillText(gradientName.toUpperCase(), canvas.width / 2, 60);

    // Create rounded gradient pills
    const pillWidth = 120;
    const pillHeight = 400;
    const spacing = 20;
    const totalWidth =
      colorStops.length * pillWidth + (colorStops.length - 1) * spacing;
    const startX = (canvas.width - totalWidth) / 2;
    const startY = 120;

    const sortedStops = [...colorStops].sort((a, b) => a.position - b.position);

    sortedStops.forEach((stop, index) => {
      const x = startX + index * (pillWidth + spacing);

      // Create individual gradient for each pill
      const gradient = ctx.createLinearGradient(
        x,
        startY,
        x,
        startY + pillHeight
      );

      // Create a gradient from the current color to a darker version
      const color = stop.color;
      const darkerColor = adjustBrightness(color, -40);

      gradient.addColorStop(0, color);
      gradient.addColorStop(1, darkerColor);

      // Draw rounded rectangle (pill shape)
      ctx.fillStyle = gradient;
      drawRoundedRect(ctx, x, startY, pillWidth, pillHeight, pillWidth / 2);
      ctx.fill();

      // Add subtle border
      ctx.strokeStyle = "rgba(255, 255, 255, 0.3)";
      ctx.lineWidth = 2;
      ctx.stroke();
    });

    // Download
    const link = document.createElement("a");
    link.download = `${gradientName.replace(/\s+/g, "-").toLowerCase()}.png`;
    link.href = canvas.toDataURL("image/png", 1.0);
    link.click();
  };

  const adjustBrightness = (color: string, amount: number) => {
    const hex = color.replace("#", "");
    const r = Math.max(
      0,
      Math.min(255, parseInt(hex.substr(0, 2), 16) + amount)
    );
    const g = Math.max(
      0,
      Math.min(255, parseInt(hex.substr(2, 2), 16) + amount)
    );
    const b = Math.max(
      0,
      Math.min(255, parseInt(hex.substr(4, 2), 16) + amount)
    );
    return `#${r.toString(16).padStart(2, "0")}${g
      .toString(16)
      .padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">
            Gradient Blender
          </h1>
          <p className="text-muted-foreground">
            Create beautiful gradients with multiple color stops
          </p>
        </div>

        {/* Main Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              Gradient Editor
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Gradient Name */}
            <div>
              <Label htmlFor="gradient-name">Gradient Name</Label>
              <Input
                id="gradient-name"
                value={gradientName}
                onChange={(e) => setGradientName(e.target.value)}
                className="mt-1"
              />
            </div>

            {/* Gradient Preview */}
            <div className="space-y-4">
              <Label>Preview</Label>
              <div
                className="w-full h-32 rounded-lg border"
                style={{ background: generateGradientCSS() }}
              />

              {/* Gradient Pills */}
              <div className="flex gap-2 flex-wrap justify-center">
                {colorStops.map((stop, index) => (
                  <div
                    key={stop.id}
                    className="w-16 h-24 rounded-full border-2 border-white shadow-lg"
                    style={{ backgroundColor: stop.color }}
                  />
                ))}
              </div>
            </div>

            {/* Direction Control */}
            <div>
              <Label>Direction: {direction}°</Label>
              <Slider
                value={[direction]}
                onValueChange={(value) => setDirection(value[0])}
                max={360}
                step={1}
                className="mt-2"
              />
            </div>

            {/* Color Stops */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>Color Stops</Label>
                <Button onClick={addColorStop} size="sm" variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Color
                </Button>
              </div>

              <div className="space-y-3">
                {colorStops.map((stop) => (
                  <div key={stop.id} className="flex items-center gap-3">
                    <input
                      type="color"
                      value={stop.color}
                      onChange={(e) =>
                        updateColorStop(stop.id, { color: e.target.value })
                      }
                      className="w-12 h-10 rounded border cursor-pointer"
                    />
                    <Input
                      type="text"
                      value={stop.color}
                      onChange={(e) =>
                        updateColorStop(stop.id, { color: e.target.value })
                      }
                      className="flex-1"
                    />
                    <div className="flex items-center gap-2 min-w-[120px]">
                      <Input
                        type="number"
                        value={stop.position}
                        onChange={(e) =>
                          updateColorStop(stop.id, {
                            position: Number(e.target.value),
                          })
                        }
                        min={0}
                        max={100}
                        className="w-16"
                      />
                      <span className="text-sm text-muted-foreground">%</span>
                    </div>
                    <Button
                      onClick={() => removeColorStop(stop.id)}
                      size="sm"
                      variant="outline"
                      disabled={colorStops.length <= 2}
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>

            {/* Actions */}
            <div className="flex flex-wrap gap-3 pt-4">
              <Button onClick={copyCSS} variant="outline">
                <Copy className="h-4 w-4 mr-2" />
                Copy CSS
              </Button>
              <Button onClick={downloadPNG}>
                <Download className="h-4 w-4 mr-2" />
                Download PNG
              </Button>
              <Button onClick={resetGradient} variant="outline">
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              <Button variant="outline">
                <Save className="h-4 w-4 mr-2" />
                Save to Collection
              </Button>
            </div>

            {/* CSS Output */}
            <div>
              <Label>CSS Code</Label>
              <div className="mt-2 p-3 bg-muted rounded-lg font-mono text-sm">
                background: {generateGradientCSS()};
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Hidden Canvas for PNG Export */}
        <canvas ref={canvasRef} style={{ display: "none" }} />
      </div>
    </div>
  );
}
