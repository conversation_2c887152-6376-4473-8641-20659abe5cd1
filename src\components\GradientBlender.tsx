"use client";

import { useState, useRef } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import {
  Download,
  Plus,
  Minus,
  Palette,
  <PERSON>otate<PERSON>c<PERSON>,
  <PERSON><PERSON>,
  Save,
} from "lucide-react";

interface ColorStop {
  id: string;
  color: string;
  position: number;
}

export default function GradientBlender() {
  const [colorStops, setColorStops] = useState<ColorStop[]>([
    { id: "1", color: "#FFD700", position: 0 }, // Gold/Yellow
    { id: "2", color: "#DC143C", position: 100 }, // Crimson
  ]);

  const [direction, setDirection] = useState(90);
  const [gradientName, setGradientName] = useState("Vector Gradient Color Set");
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const generateGradientCSS = () => {
    const sortedStops = [...colorStops].sort((a, b) => a.position - b.position);
    const colorString = sortedStops
      .map((stop) => `${stop.color} ${stop.position}%`)
      .join(", ");
    return `linear-gradient(${direction}deg, ${colorString})`;
  };

  const addColorStop = () => {
    const newId = Date.now().toString();
    const newPosition =
      colorStops.length > 0
        ? Math.max(...colorStops.map((s) => s.position)) + 10
        : 0;

    setColorStops([
      ...colorStops,
      {
        id: newId,
        color: "#000000",
        position: Math.min(newPosition, 100),
      },
    ]);
  };

  const removeColorStop = (id: string) => {
    if (colorStops.length > 2) {
      setColorStops(colorStops.filter((stop) => stop.id !== id));
    }
  };

  const updateColorStop = (id: string, updates: Partial<ColorStop>) => {
    setColorStops(
      colorStops.map((stop) =>
        stop.id === id ? { ...stop, ...updates } : stop
      )
    );
  };

  const resetGradient = () => {
    setColorStops([
      { id: "1", color: "#FFD700", position: 0 }, // Gold/Yellow
      { id: "2", color: "#DC143C", position: 100 }, // Crimson
    ]);
    setDirection(90);
  };

  const copyCSS = () => {
    const css = generateGradientCSS();
    navigator.clipboard.writeText(css);
    alert("CSS copied to clipboard!");
  };

  const drawRoundedRect = (
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    width: number,
    height: number,
    radius: number
  ) => {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
  };

  const downloadPNG = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Set canvas size for high quality
    canvas.width = 1200;
    canvas.height = 800;

    // Background
    ctx.fillStyle = "#f8f9fa";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Add title
    ctx.fillStyle = "#666";
    ctx.font = "bold 32px Arial";
    ctx.textAlign = "center";
    ctx.fillText(gradientName.toUpperCase(), canvas.width / 2, 60);

    // Create rounded gradient pills
    const pillWidth = 120;
    const pillHeight = 400;
    const spacing = 20;
    const pillCount = 7; // Fixed number of pills like in the image
    const totalWidth = pillCount * pillWidth + (pillCount - 1) * spacing;
    const startX = (canvas.width - totalWidth) / 2;
    const startY = 120;

    const sortedStops = [...colorStops].sort((a, b) => a.position - b.position);

    // Generate interpolated colors between the selected colors
    const interpolatedColors: string[] = [];

    for (let i = 0; i < pillCount; i++) {
      const position = i / (pillCount - 1); // 0 to 1
      const color = interpolateGradient(sortedStops, position);
      interpolatedColors.push(color);
    }

    interpolatedColors.forEach((color, index) => {
      const x = startX + index * (pillWidth + spacing);

      // Create individual gradient for each pill - from light to dark
      const gradient = ctx.createLinearGradient(
        x,
        startY,
        x,
        startY + pillHeight
      );

      // Create a gradient from lighter version to darker version
      const lighterColor = adjustBrightness(color, 40); // Lighter at top
      const darkerColor = adjustBrightness(color, -60); // Darker at bottom

      gradient.addColorStop(0, lighterColor);
      gradient.addColorStop(0.5, color);
      gradient.addColorStop(1, darkerColor);

      // Draw rounded rectangle (pill shape)
      ctx.fillStyle = gradient;
      drawRoundedRect(ctx, x, startY, pillWidth, pillHeight, pillWidth / 2);
      ctx.fill();

      // Add subtle white border for better definition
      ctx.strokeStyle = "rgba(255, 255, 255, 0.2)";
      ctx.lineWidth = 1;
      drawRoundedRect(ctx, x, startY, pillWidth, pillHeight, pillWidth / 2);
      ctx.stroke();
    });

    // Download
    const link = document.createElement("a");
    link.download = `${gradientName.replace(/\s+/g, "-").toLowerCase()}.png`;
    link.href = canvas.toDataURL("image/png", 1.0);
    link.click();
  };

  const interpolateGradient = (
    stops: ColorStop[],
    position: number
  ): string => {
    // Convert position (0-1) to percentage (0-100)
    const targetPosition = position * 100;

    // Find the two stops to interpolate between
    let leftStop = stops[0];
    let rightStop = stops[stops.length - 1];

    for (let i = 0; i < stops.length - 1; i++) {
      if (
        targetPosition >= stops[i].position &&
        targetPosition <= stops[i + 1].position
      ) {
        leftStop = stops[i];
        rightStop = stops[i + 1];
        break;
      }
    }

    // Calculate interpolation factor
    const range = rightStop.position - leftStop.position;
    const factor =
      range === 0 ? 0 : (targetPosition - leftStop.position) / range;

    // Interpolate between colors
    return interpolateColors(leftStop.color, rightStop.color, factor);
  };

  const interpolateColors = (
    color1: string,
    color2: string,
    factor: number
  ): string => {
    const hex1 = color1.replace("#", "");
    const hex2 = color2.replace("#", "");

    const r1 = parseInt(hex1.substring(0, 2), 16);
    const g1 = parseInt(hex1.substring(2, 4), 16);
    const b1 = parseInt(hex1.substring(4, 6), 16);

    const r2 = parseInt(hex2.substring(0, 2), 16);
    const g2 = parseInt(hex2.substring(2, 4), 16);
    const b2 = parseInt(hex2.substring(4, 6), 16);

    const r = Math.round(r1 + (r2 - r1) * factor);
    const g = Math.round(g1 + (g2 - g1) * factor);
    const b = Math.round(b1 + (b2 - b1) * factor);

    const toHex = (n: number) => {
      const hex = n.toString(16);
      return hex.length === 1 ? "0" + hex : hex;
    };

    return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
  };

  const adjustBrightness = (color: string, amount: number) => {
    const hex = color.replace("#", "");

    // Parse RGB values
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    // Adjust brightness with better clamping
    const newR = Math.max(0, Math.min(255, r + amount));
    const newG = Math.max(0, Math.min(255, g + amount));
    const newB = Math.max(0, Math.min(255, b + amount));

    // Convert back to hex
    const toHex = (n: number) => {
      const hex = Math.round(n).toString(16);
      return hex.length === 1 ? "0" + hex : hex;
    };

    return `#${toHex(newR)}${toHex(newG)}${toHex(newB)}`;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">
            Gradient Blender
          </h1>
          <p className="text-muted-foreground">
            Create beautiful gradients with multiple color stops
          </p>
        </div>

        {/* Main Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              Gradient Editor
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Gradient Name */}
            <div>
              <Label htmlFor="gradient-name">Gradient Name</Label>
              <Input
                id="gradient-name"
                value={gradientName}
                onChange={(e) => setGradientName(e.target.value)}
                className="mt-1"
              />
            </div>

            {/* Gradient Preview */}
            <div className="space-y-4">
              <Label>Preview</Label>
              <div
                className="w-full h-32 rounded-lg border"
                style={{ background: generateGradientCSS() }}
              />

              {/* Gradient Pills */}
              <div className="flex gap-2 flex-wrap justify-center">
                {colorStops.map((stop, index) => (
                  <div
                    key={stop.id}
                    className="w-16 h-24 rounded-full border-2 border-white shadow-lg"
                    style={{ backgroundColor: stop.color }}
                  />
                ))}
              </div>
            </div>

            {/* Direction Control */}
            <div>
              <Label>Direction: {direction}°</Label>
              <Slider
                value={[direction]}
                onValueChange={(value) => setDirection(value[0])}
                max={360}
                step={1}
                className="mt-2"
              />
            </div>

            {/* Color Stops */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>Color Stops</Label>
                <Button onClick={addColorStop} size="sm" variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Color
                </Button>
              </div>

              <div className="space-y-3">
                {colorStops.map((stop) => (
                  <div key={stop.id} className="flex items-center gap-3">
                    <input
                      type="color"
                      value={stop.color}
                      onChange={(e) =>
                        updateColorStop(stop.id, { color: e.target.value })
                      }
                      className="w-12 h-10 rounded border cursor-pointer"
                    />
                    <Input
                      type="text"
                      value={stop.color}
                      onChange={(e) =>
                        updateColorStop(stop.id, { color: e.target.value })
                      }
                      className="flex-1"
                    />
                    <div className="flex items-center gap-2 min-w-[120px]">
                      <Input
                        type="number"
                        value={stop.position}
                        onChange={(e) =>
                          updateColorStop(stop.id, {
                            position: Number(e.target.value),
                          })
                        }
                        min={0}
                        max={100}
                        className="w-16"
                      />
                      <span className="text-sm text-muted-foreground">%</span>
                    </div>
                    <Button
                      onClick={() => removeColorStop(stop.id)}
                      size="sm"
                      variant="outline"
                      disabled={colorStops.length <= 2}
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>

            {/* Actions */}
            <div className="flex flex-wrap gap-3 pt-4">
              <Button onClick={copyCSS} variant="outline">
                <Copy className="h-4 w-4 mr-2" />
                Copy CSS
              </Button>
              <Button onClick={downloadPNG}>
                <Download className="h-4 w-4 mr-2" />
                Download PNG
              </Button>
              <Button onClick={resetGradient} variant="outline">
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              <Button variant="outline">
                <Save className="h-4 w-4 mr-2" />
                Save to Collection
              </Button>
            </div>

            {/* CSS Output */}
            <div>
              <Label>CSS Code</Label>
              <div className="mt-2 p-3 bg-muted rounded-lg font-mono text-sm">
                background: {generateGradientCSS()};
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Hidden Canvas for PNG Export */}
        <canvas ref={canvasRef} style={{ display: "none" }} />
      </div>
    </div>
  );
}
