import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Gradient from '@/models/Gradient';
import User from '@/models/User';

// POST /api/gradients/[id]/like - Like/Unlike gradient
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const { id } = await params;
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID required' },
        { status: 400 }
      );
    }

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Find gradient
    const gradient = await Gradient.findById(id);
    if (!gradient) {
      return NextResponse.json(
        { error: 'Gradient not found' },
        { status: 404 }
      );
    }

    const isLiked = gradient.likedBy.includes(userId);

    if (isLiked) {
      // Unlike
      await Gradient.findByIdAndUpdate(id, {
        $pull: { likedBy: userId }
      });

      await User.findByIdAndUpdate(userId, {
        $pull: { favoriteGradients: id }
      });

      // Update gradient author's total likes
      await User.findByIdAndUpdate(gradient.author, {
        $inc: { 'stats.totalLikes': -1 }
      });

    } else {
      // Like
      await Gradient.findByIdAndUpdate(id, {
        $addToSet: { likedBy: userId }
      });

      await User.findByIdAndUpdate(userId, {
        $addToSet: { favoriteGradients: id }
      });

      // Update gradient author's total likes
      await User.findByIdAndUpdate(gradient.author, {
        $inc: { 'stats.totalLikes': 1 }
      });
    }

    // Get updated gradient with like count
    const updatedGradient = await Gradient.findById(id)
      .populate('author', 'username avatar')
      .lean();

    return NextResponse.json({
      gradient: updatedGradient,
      isLiked: !isLiked,
      likeCount: updatedGradient?.likedBy?.length || 0
    });

  } catch (error) {
    console.error('Error toggling like:', error);
    return NextResponse.json(
      { error: 'Failed to toggle like' },
      { status: 500 }
    );
  }
}
