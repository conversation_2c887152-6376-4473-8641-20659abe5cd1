import Header from "@/components/Header";
import Footer from "@/components/Footer";
import CollectionDetail from "@/components/CollectionDetail";

interface CollectionPageProps {
  params: {
    id: string;
  };
}

export async function generateMetadata({ params }: CollectionPageProps) {
  // In a real app, you'd fetch the collection data here
  return {
    title: `Collection - Gradient Hunt`,
    description: "View gradient collection details and gradients.",
  };
}

export default function CollectionPage({ params }: CollectionPageProps) {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        <CollectionDetail collectionId={params.id} />
      </main>
      <Footer />
    </div>
  );
}
