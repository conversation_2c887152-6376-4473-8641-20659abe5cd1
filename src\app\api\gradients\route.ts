import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Gradient from '@/models/Gradient';
import User from '@/models/User';

// GET /api/gradients - Fetch gradients with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const category = searchParams.get('category');
    const tags = searchParams.get('tags')?.split(',');
    const search = searchParams.get('search');
    const sortBy = searchParams.get('sortBy') || 'trending';
    const author = searchParams.get('author');

    // Build query
    const query: any = { isPublic: true };

    if (category && category !== 'all') {
      query.category = category;
    }

    if (tags && tags.length > 0) {
      query.tags = { $in: tags };
    }

    if (author) {
      query.author = author;
    }

    if (search) {
      query.$text = { $search: search };
    }

    // Build sort
    let sort: any = {};
    switch (sortBy) {
      case 'popular':
        sort = { 'stats.likes': -1 };
        break;
      case 'new':
        sort = { createdAt: -1 };
        break;
      case 'views':
        sort = { 'stats.views': -1 };
        break;
      case 'downloads':
        sort = { 'stats.downloads': -1 };
        break;
      default: // trending
        sort = { 'stats.views': -1, 'stats.likes': -1 };
    }

    const skip = (page - 1) * limit;

    const [gradients, total] = await Promise.all([
      Gradient.find(query)
        .populate('author', 'username avatar')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      Gradient.countDocuments(query)
    ]);

    return NextResponse.json({
      gradients,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching gradients:', error);
    return NextResponse.json(
      { error: 'Failed to fetch gradients' },
      { status: 500 }
    );
  }
}

// POST /api/gradients - Create new gradient
export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const {
      name,
      description,
      colors,
      css,
      tailwind,
      type,
      angle,
      author,
      tags,
      category,
      colorStops
    } = body;

    // Validate required fields
    if (!name || !colors || !css || !tailwind || !author || !category) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate author exists
    const user = await User.findById(author);
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid author' },
        { status: 400 }
      );
    }

    // Generate Blender colors
    const blenderColors = colors.map((color: string) => {
      const r = parseInt(color.slice(1, 3), 16) / 255;
      const g = parseInt(color.slice(3, 5), 16) / 255;
      const b = parseInt(color.slice(5, 7), 16) / 255;
      return { r, g, b, a: 1 };
    });

    // Create gradient
    const gradient = new Gradient({
      name,
      description,
      colors,
      css,
      tailwind,
      type: type || 'linear',
      angle: angle || 135,
      author,
      tags: tags || [],
      category,
      metadata: {
        colorStops: colorStops || colors.map((color: string, index: number) => ({
          color,
          position: (index / (colors.length - 1)) * 100
        })),
        blenderColors,
        accessibility: {
          colorBlindSafe: false // TODO: Implement color blind check
        }
      }
    });

    await gradient.save();

    // Update user stats
    await User.findByIdAndUpdate(author, {
      $inc: { 'stats.gradientsCreated': 1 }
    });

    // Populate author details
    await gradient.populate('author', 'username avatar');

    return NextResponse.json(gradient, { status: 201 });

  } catch (error) {
    console.error('Error creating gradient:', error);
    return NextResponse.json(
      { error: 'Failed to create gradient' },
      { status: 500 }
    );
  }
}
