import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Gradient from '@/models/Gradient';

// POST /api/gradients/[id]/download - Track download
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const { id } = await params;
    const { format } = await request.json();

    // Find gradient
    const gradient = await Gradient.findById(id);
    if (!gradient) {
      return NextResponse.json(
        { error: 'Gradient not found' },
        { status: 404 }
      );
    }

    // Increment download count
    await Gradient.findByIdAndUpdate(id, {
      $inc: { 'stats.downloads': 1 }
    });

    return NextResponse.json({
      message: 'Download tracked successfully',
      format,
      downloads: gradient.stats.downloads + 1
    });

  } catch (error) {
    console.error('Error tracking download:', error);
    return NextResponse.json(
      { error: 'Failed to track download' },
      { status: 500 }
    );
  }
}

// POST /api/gradients/[id]/copy - Track copy action
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const { id } = await params;
    const { format } = await request.json();

    // Find gradient
    const gradient = await Gradient.findById(id);
    if (!gradient) {
      return NextResponse.json(
        { error: 'Gradient not found' },
        { status: 404 }
      );
    }

    // Increment copy count
    await Gradient.findByIdAndUpdate(id, {
      $inc: { 'stats.copies': 1 }
    });

    return NextResponse.json({
      message: 'Copy tracked successfully',
      format,
      copies: gradient.stats.copies + 1
    });

  } catch (error) {
    console.error('Error tracking copy:', error);
    return NextResponse.json(
      { error: 'Failed to track copy' },
      { status: 500 }
    );
  }
}
